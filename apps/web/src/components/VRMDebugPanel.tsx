'use client';

import { useEffect, useState, useCallback } from 'react';

interface DebugLog {
  timestamp: string;
  level: string;
  message: string;
}

interface VRMDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

export default function VRMDebugPanel({ isVisible = false, onToggle }: VRMDebugPanelProps) {
  const [logs, setLogs] = useState<DebugLog[]>([]);

  // ログ追加用のコールバック（useCallbackで安定化）
  const addLog = useCallback((level: string, message: string) => {
    const newLog = {
      timestamp: new Date().toLocaleTimeString(),
      level,
      message
    };

    setLogs(prev => {
      // 最新5件の中に同じメッセージがあるかチェック
      const recentLogs = prev.slice(-5);
      const isDuplicate = recentLogs.some(log =>
        log.message === newLog.message && log.level === newLog.level
      );

      if (isDuplicate) return prev;
      return [...prev, newLog].slice(-50); // 最新50件のみ保持
    });
  }, []);

  // コンソールログ監視（isVisibleが変更された時のみ実行）
  useEffect(() => {
    if (!isVisible) return;

    // 既にオーバーライドされているかチェック
    if ((console.log as any).__vrmDebugOverridden) {
      return;
    }

    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info
    };

    // コンソールメソッドをオーバーライド
    ['log', 'warn', 'error', 'info'].forEach(method => {
      const originalMethod = (originalConsole as any)[method];

      (console as any)[method] = (...args: any[]) => {
        // 元のコンソール関数を先に実行
        const result = originalMethod.apply(console, args);

        try {
          const message = args.map(arg => {
            if (typeof arg === 'object' && arg !== null) {
              try {
                return JSON.stringify(arg, (key, value) => {
                  // circular参照の可能性があるプロパティをスキップ
                  if (typeof value === 'object' && value !== null) {
                    if (key === 'parser' || key === 'plugins' || key === 'parent' || key === 'scene') {
                      return '[Circular]';
                    }
                  }
                  return value;
                }, 2);
              } catch (error) {
                return `[Object: ${arg.constructor?.name || 'Unknown'}]`;
              }
            }
            return String(arg);
          }).join(' ');

          // VRM関連のログのみキャプチャ
          if ((method === 'error' && message.includes('VRM')) ||
              (method === 'warn' && message.includes('VRM')) ||
              (method === 'log' && (message.includes('✅ VRM') || message.includes('📦 VRM') || message.includes('🎯 VRM')))) {

            // 非同期でログを追加（レンダリングサイクルを分離）
            requestAnimationFrame(() => {
              addLog(method, message);
            });
          }
        } catch (error) {
          // ログ処理でエラーが発生しても元の処理は継続
        }

        return result;
      };

      // オーバーライド済みマーク
      (console as any)[method].__vrmDebugOverridden = true;
    });

    // postMessage を監視
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'VRM_DEBUG') {
        addLog('debug', `VRM Debug: ${JSON.stringify(event.data.data, null, 2)}`);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      // クリーンアップ
      Object.keys(originalConsole).forEach(method => {
        (console as any)[method] = (originalConsole as any)[method];
        delete (console as any)[method].__vrmDebugOverridden;
      });
      window.removeEventListener('message', handleMessage);
    };
  }, [isVisible, addLog]);

  // ログをテキスト形式でコピー
  const copyLogsToClipboard = () => {
    const logText = logs.map(log => 
      `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}`
    ).join('\n');
    
    navigator.clipboard.writeText(logText).then(() => {
      console.log('✅ デバッグログをクリップボードにコピーしました');
    }).catch(() => {
      console.error('❌ クリップボードへのコピーに失敗しました');
    });
  };

  if (!isVisible) {
    return null; // MetaStudioLayoutで管理
  }

  return (
    <div className="fixed bottom-16 left-4 z-40 bg-base-100 border-2 border-green-500 rounded-lg shadow-xl" style={{ width: '380px', height: '320px' }}>
      <div className="p-2 border-b border-base-300 flex justify-between items-center">
        <h3 className="font-bold text-sm">🐛 VRM Debug Logs</h3>
        <div className="flex gap-1">
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="ログをコピー"
            onClick={copyLogsToClipboard}
          >
            📋
          </button>
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="ログをクリア"
            onClick={() => setLogs([])}
          >
            🗑️
          </button>
          <button
            className="btn btn-xs btn-ghost tooltip tooltip-top"
            data-tip="パネルを閉じる"
            onClick={onToggle}
          >
            ✕
          </button>
        </div>
      </div>
      
      <div className="p-2 h-64 overflow-y-auto text-xs font-mono">
        {logs.length === 0 ? (
          <div className="text-base-content/50">Waiting for VRM logs...</div>
        ) : (
          logs.map((log, index) => (
            <div 
              key={index} 
              className={`mb-1 p-1 rounded ${
                log.level === 'error' ? 'bg-error/10 text-error' :
                log.level === 'warn' ? 'bg-warning/10 text-warning' :
                log.level === 'debug' ? 'bg-info/10 text-info' :
                'bg-base-200'
              }`}
            >
              <div className="text-xs opacity-50">[{log.timestamp}] {log.level}</div>
              <div className="whitespace-pre-wrap break-words">{log.message}</div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}