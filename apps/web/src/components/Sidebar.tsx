'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>Text, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart<PERSON>, <PERSON><PERSON>, Star, ArrowUpDown, Calendar, Bot, Copy, Trash2, Edit3, FolderOpen, GitBranch, Archive, FileCode, Search, Plus, X, RefreshCw, ChevronDown, ChevronRight, Clock, User, Monitor, Puzzle, TrendingUp, Smartphone } from 'lucide-react';
import { useState, useEffect, memo, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useContextMenu, ContextMenuItem } from './ContextMenu';

interface SidebarProps {
  onDashboardClick?: () => void;
  onProjectClick?: (project: string) => void;
  onLauncherClick?: () => void;
  onSystemClick?: (system: string) => void;
  onFileClick?: (fileName: string, filePath?: string) => void;
  onHomeClick?: () => void;
  projectListVersion?: number;
  lastCreatedProject?: { name: string; template: any } | null;
  onProjectNameUpdate?: (oldName: string, newName: string) => void;
  projectNameUpdate?: {oldName: string, newName: string, timestamp: number} | null;
  onSettingsClick?: () => void;
  onDebugClick?: () => void;
}

interface Project {
  id: string;
  name: string;
  type: 'mobile' | 'web' | 'ai' | 'game' | 'desktop';
  priority: 1 | 2 | 3 | 4 | 5;
  progress: number;
  updatedAt: Date;
  createdAt: Date;
  folder?: string;
}

interface FileTreeItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileTreeItem[];
  size?: number;
  level: number;
}

export default function Sidebar({ 
  onDashboardClick, 
  onProjectClick, 
  onLauncherClick, 
  onSystemClick, 
  onFileClick, 
  onHomeClick, 
  projectListVersion, 
  lastCreatedProject,
  onSettingsClick,
  onDebugClick, 
  onProjectNameUpdate, 
  projectNameUpdate 
}: SidebarProps) {
  const [activeProject, setActiveProject] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState('projects');
  const [sortBy, setSortBy] = useState<'name' | 'priority' | 'progress' | 'updatedAt' | 'createdAt'>('priority');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [editingProject, setEditingProject] = useState<string | null>(null);
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(new Set());
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [showFileActions, setShowFileActions] = useState(false);
  const [newItemName, setNewItemName] = useState('');
  const [newItemType, setNewItemType] = useState<'file' | 'folder'>('file');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingFileName, setEditingFileName] = useState<string | null>(null);
  const [dragOverPath, setDragOverPath] = useState<string | null>(null);
  const [showDirectoryPicker, setShowDirectoryPicker] = useState(false);
  const [currentRootPath, setCurrentRootPath] = useState('/Users/<USER>/Dev/meta-studio');
  const [fileItems, setFileItems] = useState<any[]>([]);
  const [filesLoading, setFilesLoading] = useState(false);
  const [expandedDirectoryFiles, setExpandedDirectoryFiles] = useState<Map<string, any[]>>(new Map());
  const router = useRouter();
  
  // コンテキストメニュー
  const { showContextMenu, hideContextMenu, ContextMenuComponent } = useContextMenu();
  
  // プロジェクト用コンテキストメニュー項目
  const getProjectContextMenuItems = (project: Project): ContextMenuItem[] => [
    {
      id: 'open',
      label: 'プロジェクトを開く',
      icon: '📂',
      action: () => onProjectClick?.(project.name)
    },
    {
      id: 'rename',
      label: 'プロジェクト名を変更',
      icon: '✏️',
      action: () => setEditingProject(project.id)
    },
    { id: 'separator-1', label: '', action: () => {}, separator: true },
    {
      id: 'priority',
      label: '優先度',
      icon: '⭐',
      submenu: [
        { id: 'priority-5', label: '★★★★★ 最高', icon: '⭐', action: () => updateProjectPriority(project.id, 5) },
        { id: 'priority-4', label: '★★★★☆ 高', icon: '⭐', action: () => updateProjectPriority(project.id, 4) },
        { id: 'priority-3', label: '★★★☆☆ 中', icon: '⭐', action: () => updateProjectPriority(project.id, 3) },
        { id: 'priority-2', label: '★★☆☆☆ 低', icon: '⭐', action: () => updateProjectPriority(project.id, 2) },
        { id: 'priority-1', label: '★☆☆☆☆ 最低', icon: '⭐', action: () => updateProjectPriority(project.id, 1) }
      ]
    },
    {
      id: 'status',
      label: 'ステータス',
      icon: '📊',
      submenu: [
        { id: 'progress-0', label: '0% 未開始', icon: '⚪', action: () => updateProjectProgress(project.id, 0) },
        { id: 'progress-25', label: '25% 開始済み', icon: '🔴', action: () => updateProjectProgress(project.id, 25) },
        { id: 'progress-50', label: '50% 進行中', icon: '🟡', action: () => updateProjectProgress(project.id, 50) },
        { id: 'progress-75', label: '75% ほぼ完了', icon: '🟠', action: () => updateProjectProgress(project.id, 75) },
        { id: 'progress-100', label: '100% 完了', icon: '🟢', action: () => updateProjectProgress(project.id, 100) }
      ]
    },
    { id: 'separator-2', label: '', action: () => {}, separator: true },
    {
      id: 'duplicate',
      label: 'プロジェクトを複製',
      icon: '📋',
      action: () => duplicateProject(project)
    },
    {
      id: 'archive',
      label: 'アーカイブ',
      icon: '📦',
      action: () => archiveProject(project.id)
    },
    { id: 'separator-3', label: '', action: () => {}, separator: true },
    {
      id: 'delete',
      label: 'プロジェクトを削除',
      icon: '🗑️',
      action: () => deleteProject(project.id),
      danger: true
    }
  ];
  
  // ファイル用コンテキストメニュー項目
  const getFileContextMenuItems = (fileName: string, filePath?: string): ContextMenuItem[] => [
    {
      id: 'open',
      label: 'ファイルを開く',
      icon: '📄',
      action: () => onFileClick?.(fileName, filePath)
    },
    {
      id: 'rename',
      label: 'ファイル名を変更',
      icon: '✏️',
      action: () => setEditingFileName(fileName)
    },
    { id: 'separator-1', label: '', action: () => {}, separator: true },
    {
      id: 'copy-path',
      label: 'パスをコピー',
      icon: '📋',
      action: async () => {
        try {
          if (filePath) {
            await navigator.clipboard.writeText(filePath);
            console.log('📋 パスコピー成功:', filePath);
            // アラートの代わりに通知システムを使用
            if (typeof window !== 'undefined' && 'showNotification' in window) {
              (window as any).showNotification('📋 パスをコピーしました', `${filePath}`);
            } else {
              alert(`パスをコピーしました: ${filePath}`);
            }
          } else {
            console.warn('ファイルパスが未定義です');
            alert('ファイルパスが利用できません');
          }
        } catch (error) {
          console.error('パスコピーエラー:', error);
          alert('パスのコピーに失敗しました');
        }
      }
    },
    {
      id: 'copy-name',
      label: 'ファイル名をコピー',
      icon: '📝',
      action: async () => {
        try {
          await navigator.clipboard.writeText(fileName);
          console.log('📝 ファイル名コピー成功:', fileName);
          alert(`ファイル名をコピーしました: ${fileName}`);
        } catch (error) {
          console.error('ファイル名コピーエラー:', error);
          alert('ファイル名のコピーに失敗しました');
        }
      }
    },
    { id: 'separator-2', label: '', action: () => {}, separator: true },
    {
      id: 'move',
      label: 'ファイルを移動',
      icon: '📁',
      action: async () => {
        const newPath = prompt(`${fileName} を移動する先のパスを入力してください:`, filePath ? filePath.replace(fileName, '') : '');
        if (newPath && newPath.trim()) {
          try {
            const response = await fetch('/api/files', {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                action: 'move',
                oldPath: filePath,
                newPath: newPath + fileName 
              })
            });
            if (response.ok) {
              console.log('📁 ファイル移動成功:', fileName, '→', newPath);
              alert(`${fileName} を ${newPath} に移動しました`);
              // ファイルリストを更新
              location.reload();
            } else {
              throw new Error('移動に失敗しました');
            }
          } catch (error) {
            console.error('ファイル移動エラー:', error);
            alert(`ファイル移動に失敗しました: ${error}`);
          }
        }
      }
    },
    {
      id: 'duplicate',
      label: 'ファイルを複製',
      icon: '📄',
      action: async () => {
        const newName = prompt(`複製するファイルの名前を入力してください:`, fileName.replace(/\.([^.]+)$/, '_copy.$1'));
        if (newName && newName.trim()) {
          try {
            const response = await fetch('/api/files', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                action: 'duplicate',
                sourcePath: filePath,
                newName: newName 
              })
            });
            if (response.ok) {
              console.log('📄 ファイル複製成功:', fileName, '→', newName);
              alert(`${fileName} を ${newName} として複製しました`);
              // ファイルリストを更新
              location.reload();
            } else {
              throw new Error('複製に失敗しました');
            }
          } catch (error) {
            console.error('ファイル複製エラー:', error);
            alert(`ファイル複製に失敗しました: ${error}`);
          }
        }
      }
    },
    { id: 'separator-3', label: '', action: () => {}, separator: true },
    {
      id: 'delete',
      label: 'ファイルを削除',
      icon: '🗑️',
      action: async () => {
        if (confirm(`${fileName}を削除しますか？この操作は元に戻せません。`)) {
          try {
            const response = await fetch('/api/files', {
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                path: filePath 
              })
            });
            if (response.ok) {
              console.log('🗑️ ファイル削除成功:', fileName, filePath);
              alert(`${fileName} を削除しました`);
              // ファイルリストを更新
              location.reload();
            } else {
              throw new Error('削除に失敗しました');
            }
          } catch (error) {
            console.error('ファイル削除エラー:', error);
            alert(`ファイル削除に失敗しました: ${error}`);
          }
        }
      },
      danger: true
    }
  ];
  
  // コンテキストメニュー用ヘルパー関数
  const updateProjectPriority = (projectId: string, priority: 1 | 2 | 3 | 4 | 5) => {
    console.log(`🎯 優先度変更: Project ${projectId} → Priority ${priority}`);
    alert(`プロジェクト優先度を ${priority} に変更しました`);
    // TODO: 実際のプロジェクトデータ更新を実装
  };
  
  const updateProjectProgress = (projectId: string, progress: number) => {
    console.log(`📊 進捗更新: Project ${projectId} → ${progress}%`);
    alert(`プロジェクト進捗を ${progress}% に更新しました`);
    // TODO: 実際のプロジェクトデータ更新を実装
  };
  
  const duplicateProject = (project: Project) => {
    console.log('📋 プロジェクト複製:', project.name);
    alert(`プロジェクト "${project.name}" を複製しました`);
    // TODO: プロジェクト複製機能を実装
  };
  
  const archiveProject = (projectId: string) => {
    console.log('📦 プロジェクトアーカイブ:', projectId);
    alert('プロジェクトをアーカイブしました');
    // TODO: アーカイブ機能を実装
  };
  
  const deleteProject = (projectId: string) => {
    if (confirm('このプロジェクトを削除しますか？この操作は元に戻せません。')) {
      console.log('🗑️ プロジェクト削除:', projectId);
      alert('プロジェクトを削除しました');
      // TODO: プロジェクト削除機能を実装
    }
  };

  // プロジェクトタイプごとのグラデーション色
  const getProjectGradient = (type: Project['type']) => {
    switch (type) {
      case 'mobile': return 'bg-gradient-to-r from-blue-500/20 to-blue-600/30';
      case 'ai': return 'bg-gradient-to-r from-pink-500/20 to-pink-600/30';
      case 'web': return 'bg-gradient-to-r from-cyan-500/20 to-cyan-600/30';
      case 'game': return 'bg-gradient-to-r from-red-500/20 to-red-600/30';
      case 'desktop': return 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/30';
      default: return 'bg-gradient-to-r from-gray-500/20 to-gray-600/30';
    }
  };

  // デフォルトプロジェクト
  const defaultProjects: Project[] = [
    {
      id: '1',
      name: '瞑想アプリ_projext',
      type: 'mobile',
      priority: 5,
      progress: 75,
      updatedAt: new Date('2024-01-01T10:00:00Z'),
      createdAt: new Date('2023-12-15T09:00:00Z'),
      folder: '/Users/<USER>/Dev/meditation-app'
    },
    {
      id: '2',
      name: '投資bot_projext',
      type: 'ai',
      priority: 4,
      progress: 90,
      updatedAt: new Date('2024-01-01T08:00:00Z'),
      createdAt: new Date('2023-12-10T14:30:00Z'),
      folder: '/Users/<USER>/Dev/trading-bot'
    },
    {
      id: '3',
      name: 'iS_streamer_projext',
      type: 'web',
      priority: 3,
      progress: 25,
      updatedAt: new Date('2023-12-31T10:00:00Z'),
      createdAt: new Date('2023-12-20T16:45:00Z'),
      folder: '/Users/<USER>/Dev/is-streamer'
    },
    {
      id: '4',
      name: 'MetaStudio_projext',
      type: 'web',
      priority: 5,
      progress: 85,
      updatedAt: new Date('2024-01-15T12:00:00Z'),
      createdAt: new Date('2023-11-01T09:00:00Z'),
      folder: '/Users/<USER>/Dev/meta-studio'
    }
  ];

  const [projects, setProjects] = useState<Project[]>(defaultProjects);

  // プロジェクトの実際の統計情報を取得
  const updateProjectStats = async (project: Project) => {
    if (!project.folder) return project;
    
    try {
      const response = await fetch(`/api/files?path=${encodeURIComponent(project.folder)}&stats=true`);
      const data = await response.json();
      
      if (data.createdAt && data.updatedAt) {
        return {
          ...project,
          createdAt: new Date(data.createdAt),
          updatedAt: new Date(data.updatedAt)
        };
      }
    } catch (error) {
      console.error('Failed to get project stats:', error);
    }
    
    return project;
  };

  // プロジェクト統計情報を更新
  useEffect(() => {
    const updateAllProjectStats = async () => {
      const updatedProjects = await Promise.all(
        projects.map(project => updateProjectStats(project))
      );
      setProjects(updatedProjects);
    };

    // 初回ロード時に実行
    updateAllProjectStats();
  }, []); // 空配列で初回のみ実行

  // ファイルシステムAPI読み込み - currentRootPathの変更を監視
  useEffect(() => {
    const loadFiles = async () => {
      setFilesLoading(true);
      try {
        console.log('Loading files from API for path:', currentRootPath);
        
        // 現在のルートパスから相対パスを計算
        const relativePath = currentRootPath === '/Users/<USER>/Dev/meta-studio' ? '/' : 
                             currentRootPath.replace('/Users/<USER>/Dev/meta-studio', '') || '/';
        
        const response = await fetch(`/api/files?path=${encodeURIComponent(relativePath)}`);
        const data = await response.json();
        console.log('API response for path', relativePath, ':', data);
        
        if (data.items) {
          console.log('Setting file items:', data.items.length, 'items');
          setFileItems(data.items);
        } else {
          console.log('No items in response:', data);
          setFileItems([]);
        }
      } catch (error) {
        console.error('Failed to load files:', error);
        setFileItems([]);
      } finally {
        setFilesLoading(false);
      }
    };

    loadFiles();
  }, [currentRootPath]); // currentRootPathの変更を監視

  // ファイル作成関数の改良版
  const handleCreateFile = async () => {
    if (!newItemName.trim()) return;

    try {
      console.log('Creating file/folder:', { type: newItemType, name: newItemName, path: currentRootPath });
      
      const response = await fetch('/api/files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: newItemType === 'file' ? 'create_file' : 'create_folder',
          path: '/', // ルートパスを使用
          name: newItemName,
          content: newItemType === 'file' ? getDefaultFileContent(newItemName) : undefined,
        }),
      });

      console.log('Create response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('Create response:', result);

      if (result.success) {
        console.log('File/folder created successfully');
        setNewItemName('');
        setShowCreateDialog(false);
        
        // ファイル一覧を再読込 - 少し遅延を入れて確実に反映
        setTimeout(async () => {
          await loadFiles();
        }, 500);
      } else {
        console.error('Create failed:', result);
        alert(`作成中にエラーが発生しました: ${result.error || '不明なエラー'}`);
      }
    } catch (error) {
      console.error('File creation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'ネットワークエラー';
      alert(`作成中にエラーが発生しました: ${errorMessage}`);
    }
  };

  const loadFiles = async () => {
    setFilesLoading(true);
    try {
      console.log('Reloading files from API for current path:', currentRootPath);
      
      // 現在のルートパスから相対パスを計算
      const relativePath = currentRootPath === '/Users/<USER>/Dev/meta-studio' ? '/' : 
                           currentRootPath.replace('/Users/<USER>/Dev/meta-studio', '') || '/';
      
      const response = await fetch(`/api/files?path=${encodeURIComponent(relativePath)}`, {
        cache: 'no-cache', // キャッシュを無効化
        headers: {
          'Cache-Control': 'no-cache',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Reload API response for path', relativePath, ':', data);
      
      if (data.items) {
        console.log('Setting file items:', data.items.length, 'items');
        setFileItems(data.items);
      } else {
        console.log('No items in reload response');
        setFileItems([]);
      }
    } catch (error) {
      console.error('Failed to reload files:', error);
      setFileItems([]);
    } finally {
      setFilesLoading(false);
    }
  };

  // 新規プロジェクト追加の監視
  useEffect(() => {
    if (lastCreatedProject) {
      const newProject: Project = {
        id: Date.now().toString(),
        name: lastCreatedProject.name,
        type: lastCreatedProject.template?.type || 'web',
        priority: 5,
        progress: 0,
        updatedAt: new Date(),
        createdAt: new Date()
      };

      setProjects(prev => {
        const existingIndex = prev.findIndex(p => p.name === newProject.name);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = { ...updated[existingIndex], updatedAt: new Date() };
          return updated;
        } else {
          return [newProject, ...prev];
        }
      });

      setTimeout(() => {
        setActiveProject(lastCreatedProject.name);
      }, 100);
    }
  }, [projectListVersion, lastCreatedProject]);

  // プロジェクト名更新の監視
  useEffect(() => {
    if (projectNameUpdate) {
      setProjects(prev => prev.map(p => 
        p.name === projectNameUpdate.oldName 
          ? { ...p, name: projectNameUpdate.newName, updatedAt: new Date() }
          : p
      ));
      
      if (activeProject === projectNameUpdate.oldName) {
        setActiveProject(projectNameUpdate.newName);
      }
    }
  }, [projectNameUpdate, activeProject]);

  // プロジェクトソート処理
  const sortedProjects = useMemo(() => {
    const sorted = [...projects].sort((a, b) => {
      let aVal: any, bVal: any;
      
      switch (sortBy) {
        case 'name':
          aVal = a.name.toLowerCase();
          bVal = b.name.toLowerCase();
          break;
        case 'priority':
          aVal = a.priority;
          bVal = b.priority;
          break;
        case 'progress':
          aVal = a.progress;
          bVal = b.progress;
          break;
        case 'updatedAt':
          aVal = a.updatedAt.getTime();
          bVal = b.updatedAt.getTime();
          break;
        case 'createdAt':
          aVal = a.createdAt.getTime();
          bVal = b.createdAt.getTime();
          break;
        default:
          return 0;
      }
      
      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
    
    return sorted;
  }, [projects, sortBy, sortDirection]);

  const getSortLabel = (field: string) => {
    const labels: Record<string, string> = {
      'name': '名前',
      'priority': '優先度',
      'progress': '進捗',
      'updatedAt': '編集日',
      'createdAt': '作成日'
    };
    return labels[field] || field;
  };

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('desc');
    }
  };

  const getTypeColor = (type: Project['type']) => {
    switch (type) {
      case 'mobile': return 'text-primary';
      case 'ai': return 'text-secondary';
      case 'web': return 'text-accent';
      case 'game': return 'text-error';
      case 'desktop': return 'text-warning';
      default: return 'text-neutral';
    }
  };

  const getSortIcon = (sortBy: string, project: Project) => {
    switch (sortBy) {
      case 'name':
        return <FileText size={12} className="text-base-content/60" />;
      case 'priority':
        return (
          <div className="flex items-center gap-0.5" onClick={(e) => handleStarRating(e, project.id)}>
            {[1, 2, 3, 4, 5].map(star => (
              <Star 
                key={star}
                size={10} 
                className={star <= project.priority ? "text-warning fill-warning" : "text-base-content/30"}
              />
            ))}
          </div>
        );
      case 'progress':
        return <BarChart3 size={12} className="text-success" />;
      case 'updatedAt':
        return (
          <span className="text-xs text-info bg-info/10 px-1 rounded">
            {project.updatedAt.toLocaleDateString('ja-JP', { 
              month: 'numeric', 
              day: 'numeric' 
            })}
          </span>
        );
      case 'createdAt':
        return (
          <span className="text-xs text-secondary bg-secondary/10 px-1 rounded">
            {project.createdAt.toLocaleDateString('ja-JP', { 
              month: 'numeric', 
              day: 'numeric' 
            })}
          </span>
        );
      default:
        return <span className="text-xs">P{project.priority}</span>;
    }
  };

  const handleStarRating = (e: React.MouseEvent, projectId: string) => {
    e.stopPropagation();
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const starWidth = rect.width / 5;
    const newRating = Math.ceil(clickX / starWidth);
    
    setProjects(prev => prev.map(p => 
      p.id === projectId ? { ...p, priority: newRating } : p
    ));
  };

  const getDefaultFileContent = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'md':
        return `# ${fileName.replace('.md', '')}\n\n新規マークダウンファイルです。\n\n## 概要\n\nここに内容を記述してください。\n`;
      case 'txt':
        return `新規テキストファイル: ${fileName}\n\n作成日: ${new Date().toLocaleString()}\n`;
      case 'js':
      case 'jsx':
        return `// ${fileName}\n// 作成日: ${new Date().toLocaleString()}\n\nconsole.log('Hello, ${fileName}!');\n`;
      case 'ts':
      case 'tsx':
        return `// ${fileName}\n// 作成日: ${new Date().toLocaleString()}\n\nexport default function ${fileName.split('.')[0]}() {\n  console.log('Hello, TypeScript!');\n}\n`;
      case 'json':
        return `{\n  "name": "${fileName.replace('.json', '')}",\n  "version": "1.0.0",\n  "description": "新規JSONファイル",\n  "created": "${new Date().toISOString()}"\n}\n`;
      case 'yaml':
      case 'yml':
        return `# ${fileName}\n# 作成日: ${new Date().toLocaleString()}\n\nname: ${fileName.replace(/\.(yaml|yml)$/, '')}\nversion: 1.0.0\ndescription: 新規YAMLファイル\ncreated: ${new Date().toISOString()}\n`;
      default:
        return `新規ファイル: ${fileName}\n作成日: ${new Date().toLocaleString()}\n\nこのファイルに内容を追加してください。\n`;
    }
  };

  const getFileIcon = (fileName: string, isDirectory: boolean) => {
    if (isDirectory) return <Folder size={16} className="text-info" />;
    if (fileName.endsWith('.md')) return <FileText size={16} className="text-info" />;
    if (fileName.endsWith('.yaml') || fileName.endsWith('.yml')) return <FileText size={16} className="text-warning" />;
    if (fileName.endsWith('.json')) return <FileText size={16} className="text-success" />;
    if (fileName.endsWith('.ts') || fileName.endsWith('.tsx')) return <FileText size={16} className="text-primary" />;
    if (fileName.endsWith('.js') || fileName.endsWith('.jsx')) return <FileText size={16} className="text-warning" />;
    return <FileText size={16} className="text-base-content" />;
  };

  // インクリメンタル検索の処理
  const filteredFiles = useMemo(() => {
    if (!searchQuery.trim()) return fileItems;
    
    const query = searchQuery.toLowerCase();
    return fileItems.filter(item => 
      item.name.toLowerCase().includes(query) ||
      (item.path && item.path.toLowerCase().includes(query))
    );
  }, [fileItems, searchQuery]);

  // セクション折りたたみ処理
  const toggleSection = (sectionName: string) => {
    setCollapsedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionName)) {
        newSet.delete(sectionName);
      } else {
        newSet.add(sectionName);
      }
      return newSet;
    });
  };

  const isCollapsed = (sectionName: string) => collapsedSections.has(sectionName);

  const handleDirectoryClick = async (path: string, isDirectory: boolean) => {
    if (isDirectory) {
      const isExpanded = expandedFolders.has(path);
      if (isExpanded) {
        setExpandedFolders(prev => {
          const newSet = new Set(prev);
          newSet.delete(path);
          return newSet;
        });
      } else {
        setExpandedFolders(prev => new Set([...prev, path]));
        try {
          const response = await fetch(`/api/files?path=${encodeURIComponent(path)}`);
          const data = await response.json();
          if (data.items) {
            setExpandedDirectoryFiles(prev => {
              const newMap = new Map(prev);
              newMap.set(path, data.items);
              return newMap;
            });
          }
        } catch (error) {
          console.error('Failed to load directory:', error);
        }
      }
    } else {
      onFileClick?.(path.split('/').pop() || '', path);
    }
  };

  const renderFileTree = (items: any[], level: number = 0) => {
    if (!items) return null;
    
    return items.map((item, index) => {
      const isExpanded = expandedFolders.has(item.path);
      const childItems = expandedDirectoryFiles.get(item.path);
      const indent = level * 16;
      
      return (
        <div key={`${item.path}-${index}`}>
          <div
            className="flex items-center gap-2 p-1 rounded cursor-pointer hover:bg-base-200/50"
            style={{ paddingLeft: `${8 + indent}px` }}
            onClick={() => handleDirectoryClick(item.path, item.type === 'directory')}
            onContextMenu={(e) => showContextMenu(e, getFileContextMenuItems(item.name, item.path))}
          >
            {getFileIcon(item.name, item.type === 'directory')}
            <span className="text-sm truncate">{item.name}</span>
            {item.type === 'directory' && (
              <span className="text-xs text-base-content/50 ml-auto">
                {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
              </span>
            )}
          </div>
          {item.type === 'directory' && isExpanded && childItems && (
            <div>
              {renderFileTree(childItems, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <div className="h-full flex flex-col p-4 neo-glass relative">
      {/* メタスタジオアイコン & タイトル */}
      <div className="mb-6">
        <div 
          className="flex items-center gap-3 mb-2 p-3 rounded-lg neo-depth cursor-pointer hover:neo-hover transition-all"
          onClick={onHomeClick}
          title="ダッシュボードに戻る"
        >
          <div className="relative w-10 h-10">
            <Image
              src="/meta-studio-logo.png"
              alt="Meta Studio Logo"
              width={40}
              height={40}
              className="rounded-full"
            />
          </div>
          <div>
            <h1 className="text-lg font-bold">メタスタジオ</h1>
            <p className="text-xs text-base-content/70">
              脳内現実化ツール
            </p>
          </div>
        </div>
      </div>

      {/* プロジェクト管理エリア */}
      <div className="flex-1 overflow-y-auto">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-base-content/80 hover:text-base-content"
              onClick={() => toggleSection('projects')}
            >
              {isCollapsed('projects') ? <ChevronRight size={14} /> : <ChevronDown size={14} />}
              プロジェクト
            </button>
            <div className="flex items-center gap-2">
              {/* ソート機能 */}
              <div className="dropdown dropdown-end">
                <button 
                  tabIndex={0} 
                  className="btn btn-xs btn-ghost hover:btn-primary"
                  title={`${getSortLabel(sortBy)}${sortDirection === 'desc' ? '↓' : '↑'}`}
                >
                  <ArrowUpDown size={12} />
                  {sortDirection === 'desc' ? '↓' : '↑'}
                </button>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                  {['name', 'priority', 'progress', 'updatedAt', 'createdAt'].map(field => (
                    <li key={field}>
                      <button
                        className={`text-xs ${sortBy === field ? 'bg-primary text-primary-content' : ''}`}
                        onClick={() => handleSort(field as typeof sortBy)}
                      >
                        {getSortLabel(field)}
                        {sortBy === field && (
                          <span className="ml-auto">
                            {sortDirection === 'desc' ? '↓' : '↑'}
                          </span>
                        )}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
              <button
                className="btn btn-xs btn-ghost text-primary neo-hover"
                onClick={() => onSystemClick?.('管理')}
                title="管理ダッシュボード"
              >
                管理
              </button>
            </div>
          </div>
          
          {!isCollapsed('projects') && (
            <div className="space-y-1">
              {sortedProjects.map(project => (
                <div 
                  key={project.id}
                  className={`${getProjectGradient(project.type)} flex flex-col gap-1 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 ${
                    activeProject === project.name ? 'ring-1 ring-primary' : 'hover:bg-base-300'
                  }`}
                  onClick={() => {
                    setActiveProject(project.name);
                    onProjectClick?.(project.name);
                  }}
                  onContextMenu={(e) => showContextMenu(e, getProjectContextMenuItems(project))}
                >
                  <div className="flex items-center gap-2">
                    <Folder size={16} className={getTypeColor(project.type)} />
                    <span className="text-sm font-medium flex-1 truncate">
                      {project.name}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className={`badge badge-xs ${getTypeColor(project.type).replace('text-', 'badge-')}`}>
                      {project.type}
                    </span>
                    <div className="flex items-center gap-1">
                      {getSortIcon(sortBy, project)}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-base-content/20 rounded-full h-1">
                      <div 
                        className={`h-1 rounded-full ${getTypeColor(project.type).replace('text-', 'bg-')}`}
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                    <span className="text-xs text-base-content/60 w-8 text-right">
                      {project.progress}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* ファイルエクスプローラー */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-base-content/80 hover:text-base-content"
              onClick={() => toggleSection('files')}
            >
              {isCollapsed('files') ? <ChevronRight size={14} /> : <ChevronDown size={14} />}
              ファイルエクスプローラー
            </button>
            
            <div className="flex items-center gap-1">
              <button
                className="btn btn-xs btn-ghost hover:btn-success"
                onClick={() => setShowCreateDialog(true)}
                title="新規ファイル/フォルダ作成"
              >
                <Plus size={12} />
              </button>
              <button
                className="btn btn-xs btn-ghost hover:btn-info"
                onClick={loadFiles}
                title="再読込"
              >
                <RefreshCw size={12} />
              </button>
            </div>
          </div>

          {!isCollapsed('files') && (
            <>
              {/* 検索バー */}
              <div className="mb-2">
                <div className="relative">
                  <Search size={14} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-base-content/50" />
                  <input
                    type="text"
                    placeholder="ファイル検索..."
                    className="input input-xs w-full pl-8 bg-base-200/50"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {searchQuery && (
                    <button
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                      onClick={() => setSearchQuery('')}
                    >
                      <X size={12} />
                    </button>
                  )}
                </div>
              </div>

              {/* パス表示と変更 */}
              <div className="mb-2 text-xs">
                <button
                  className="flex items-center gap-1 px-2 py-1 rounded bg-base-200/50 hover:bg-base-200 text-base-content/70 truncate"
                  onClick={() => {
                    const newPath = prompt('ディレクトリパスを入力してください:', currentRootPath);
                    if (newPath && newPath !== currentRootPath) {
                      console.log('Changing directory from', currentRootPath, 'to', newPath);
                      setCurrentRootPath(newPath);
                      // 折り畳み状態をリセット
                      setExpandedFolders(new Set());
                      setExpandedDirectoryFiles(new Map());
                    }
                  }}
                  title="ディレクトリ変更"
                >
                  <Folder size={12} />
                  <span className="truncate">
                    📁{currentRootPath.replace('/Users/<USER>/Dev/meta-studio', '') || '/meta-studio'}
                  </span>
                </button>
              </div>

              {/* ファイル一覧 */}
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {filesLoading ? (
                  <div className="text-xs text-center text-base-content/50 py-2">
                    <div className="loading loading-spinner loading-sm mr-2"></div>
                    読み込み中...
                  </div>
                ) : filteredFiles.length > 0 ? (
                  <>
                    <div className="text-xs text-base-content/40 mb-1">
                      {filteredFiles.length}個のファイル/フォルダ
                    </div>
                    {renderFileTree(filteredFiles)}
                  </>
                ) : (
                  <div className="text-xs text-center text-base-content/50 py-2">
                    {searchQuery ? '検索結果がありません' : 'ファイルが見つかりません'}
                    <br />
                    <button 
                      className="btn btn-xs btn-ghost mt-1"
                      onClick={loadFiles}
                    >
                      再読込
                    </button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* アプリドック */}
        <div className="mb-4">
          <div 
            className="flex items-center justify-center p-4 rounded-lg cursor-pointer transition-all neo-depth hover:scale-105 bg-gradient-to-r from-primary/20 to-secondary/20 hover:from-primary/30 hover:to-secondary/30 border-2 border-primary/30"
            onClick={() => onSystemClick?.('アプリドック')}
            title="開発アプリ管理・ランチャー"
          >
            <div className="flex items-center gap-3">
              <Rocket size={20} className="text-primary brightness-150" />
              <div className="text-center">
                <div className="text-sm font-bold text-primary">アプリドック</div>
                <div className="text-xs text-base-content/70">開発アプリ管理</div>
              </div>
            </div>
          </div>
        </div>

        {/* システムツール */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <button
              className="flex items-center gap-2 text-sm font-semibold text-base-content/80 hover:text-base-content"
              onClick={() => toggleSection('systemTools')}
            >
              {isCollapsed('systemTools') ? <ChevronRight size={14} /> : <ChevronDown size={14} />}
              システムツール
            </button>
          </div>

          {!isCollapsed('systemTools') && (
            <div className="space-y-1">
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('エディター')}
              >
                <FileText size={16} className="text-info" />
                <span className="text-sm font-medium">エディター</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('インボックス')}
              >
                <FileText size={16} className="text-warning" />
                <span className="text-sm font-medium">インボックス</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('ブラウザ自動化')}
              >
                <Monitor size={16} className="text-accent" />
                <span className="text-sm font-medium">ブラウザ自動化</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-gradient-to-r from-primary/20 to-secondary/20 border border-primary/30 hover:bg-gradient-to-r hover:from-primary/30 hover:to-secondary/30"
                onClick={() => onSystemClick?.('対談スタジオ')}
              >
                <Bot size={16} className="text-primary brightness-150" />
                <span className="text-sm font-medium">🎭 キャラクター対談スタジオ</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('バックアップ')}
              >
                <Copy size={16} className="text-warning" />
                <span className="text-sm font-medium">バックアップ</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('エージェント管理')}
              >
                <Bot size={16} className="text-secondary brightness-125" />
                <span className="text-sm font-medium">エージェント管理</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('プラグイン')}
              >
                <Puzzle size={16} className="text-info brightness-125" />
                <span className="text-sm font-medium">拡張プラグイン</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('統計サマリー')}
              >
                <TrendingUp size={16} className="text-accent brightness-125" />
                <span className="text-sm font-medium">統計サマリー</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('モバイル')}
              >
                <Smartphone size={16} className="text-warning brightness-125" />
                <span className="text-sm font-medium">モバイル</span>
              </div>
              <div 
                className="flex items-center gap-2 p-2 rounded cursor-pointer transition-all neo-depth hover:scale-105 bg-base-100 hover:bg-base-300"
                onClick={() => onSystemClick?.('システム概要')}
              >
                <BarChart3 size={16} className="text-success brightness-125" />
                <span className="text-sm font-medium">システム概要</span>
              </div>
            </div>
          )}
        </div>
      </div>


      {/* ファイル作成ダイアログ */}
      {showCreateDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-base-100 p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
            <h3 className="text-lg font-bold mb-4">新規作成</h3>
            
            <div className="space-y-4">
              <div>
                <label className="label">
                  <span className="label-text">種類</span>
                </label>
                <div className="flex gap-2">
                  <label className="label cursor-pointer">
                    <input
                      type="radio"
                      name="itemType"
                      className="radio radio-primary"
                      checked={newItemType === 'file'}
                      onChange={() => setNewItemType('file')}
                    />
                    <span className="label-text ml-2">ファイル</span>
                  </label>
                  <label className="label cursor-pointer">
                    <input
                      type="radio"
                      name="itemType"
                      className="radio radio-primary"
                      checked={newItemType === 'folder'}
                      onChange={() => setNewItemType('folder')}
                    />
                    <span className="label-text ml-2">フォルダ</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text">名前</span>
                </label>
                <input
                  type="text"
                  className="input input-bordered w-full"
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  placeholder={newItemType === 'file' ? 'example.txt' : 'folder-name'}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleCreateFile();
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                className="btn btn-ghost"
                onClick={() => {
                  setShowCreateDialog(false);
                  setNewItemName('');
                }}
              >
                キャンセル
              </button>
              <button
                className="btn btn-primary"
                onClick={handleCreateFile}
                disabled={!newItemName.trim()}
              >
                作成
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* 右下ボタン群 */}
      <div className="absolute bottom-4 right-4 flex gap-2">
        {/* デバッグボタン */}
        <button
          onClick={onDebugClick}
          className="btn btn-ghost btn-sm shadow-lg"
          title="VRMデバッグログ"
        >
          🐛
        </button>
        {/* 設定ボタン */}
        <button
          onClick={onSettingsClick}
          className="btn btn-ghost btn-sm shadow-lg"
          title="設定"
        >
          ⚙️
        </button>
      </div>

      {/* コンテキストメニュー */}
      <ContextMenuComponent />
    </div>
  );
}