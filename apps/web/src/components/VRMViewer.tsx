'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

// 統一モデルシステムのインポート
import { universalModelLoader } from '@/lib/UniversalModelLoader';
import { VRMLoaderPlugin } from '@/lib/plugins/VRMLoaderPlugin';
import { UniversalModelController } from '@/lib/UniversalModelController';
import { UniversalModel, ModelFormat } from '@/types/UniversalModel';

interface FaceTrackingData {
  happy: number;
  angry: number;
  sad: number;
  relaxed: number;
  surprised: number;
  aa: number;
  ih: number;
  ou: number;
  ee: number;
  oh: number;
  eyeBlinkLeft: number;
  eyeBlinkRight: number;
  headRotationX: number;
  headRotationY: number;
  headRotationZ: number;
}

interface VRMViewerProps {
  vrmUrl?: string;
  vrmArrayBuffer?: ArrayBuffer; // ArrayBufferデータ
  agentId: string;
  lipSyncValue?: number;
  expressionValue?: number;
  isActive?: boolean;
  className?: string;
  showControls?: boolean;
  viewMode?: 'bust' | 'full';
  forceRender?: boolean;
  onRenderComplete?: () => void;
  faceTrackingData?: FaceTrackingData | null;
  onVRMUpload?: (file: File) => void;
}

// パフォーマンス設定
const PERFORMANCE_CONFIG = {
  TARGET_FPS: 60,
  MAX_TEXTURE_SIZE: 1024,
  ANIMATION_ENABLED: true,
  SHADOW_MAP_SIZE: 512,
  FRUSTUM_CULLING: true,
  LOD_ENABLED: true
};

// LocalAIVtuber & aituber-kit参考: 高度なVRM制御
const VRM_CONTROL_CONFIG = {
  MOUTH_SYNC_SENSITIVITY: 0.8,
  EXPRESSION_INTENSITY: 1.0,
  BLINK_FREQUENCY: 3000, // 3秒間隔
  IDLE_ANIMATION_SPEED: 0.5,
  EMOTION_TRANSITION_SPEED: 0.3,
  LIP_SYNC_SMOOTHING: 0.7
};

// 感情表現マッピング（LocalAIVtuber参考）
const EMOTION_EXPRESSIONS = {
  neutral: { happy: 0, angry: 0, sad: 0, surprised: 0, relaxed: 0.3 },
  happy: { happy: 1.0, angry: 0, sad: 0, surprised: 0, relaxed: 0 },
  sad: { happy: 0, angry: 0, sad: 1.0, surprised: 0, relaxed: 0 },
  angry: { happy: 0, angry: 1.0, sad: 0, surprised: 0, relaxed: 0 },
  surprised: { happy: 0, angry: 0, sad: 0, surprised: 1.0, relaxed: 0 },
  thinking: { happy: 0, angry: 0, sad: 0.2, surprised: 0, relaxed: 0.8 },
  speaking: { happy: 0.3, angry: 0, sad: 0, surprised: 0, relaxed: 0.2 }
};

// Three.jsライブラリキャッシュ
let THREE_CACHE: any = null;
let GLTFLoader_CACHE: any = null;
let VRMLoader_CACHE: any = null;

export default function VRMViewer({
  vrmUrl,
  vrmArrayBuffer,
  agentId,
  lipSyncValue = 80,
  expressionValue = 90,
  isActive = false,
  className = "",
  showControls = true,
  viewMode = 'bust',
  forceRender = false,
  onRenderComplete,
  faceTrackingData,
  onVRMUpload,
  // LocalAIVtuber & aituber-kit参考: 高度な制御
  emotion = 'neutral',
  voiceVolume = 0,
  enableAutoBlinking = true,
  enableIdleAnimation = true,
  onExpressionChange
}: VRMViewerProps & {
  emotion?: keyof typeof EMOTION_EXPRESSIONS;
  voiceVolume?: number;
  enableAutoBlinking?: boolean;
  enableIdleAnimation?: boolean;
  onExpressionChange?: (expression: string, intensity: number) => void;
}) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  // viewModeはpropsから受け取るため、内部のstateは削除
  const [controlsExpanded, setControlsExpanded] = useState(false);
  
  // ズーム制御
  const [zoomLevel, setZoomLevel] = useState(1.0);
  const zoomRef = useRef(1.0);
  const sceneRef = useRef<any>(null);
  const rendererRef = useRef<any>(null);
  const cameraRef = useRef<any>(null);
  const vrmRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const loadingRef = useRef(false);

  // 統一モデルシステム用のrefs
  const universalModelRef = useRef<UniversalModel | null>(null);
  const modelControllerRef = useRef<UniversalModelController | null>(null);
  const isUniversalSystemInitialized = useRef(false);

  // レンダリング制御のためのrefs
  const animationIdRef = useRef<number | null>(null);
  const lastFrameTimeRef = useRef(0);
  const needsRenderRef = useRef(true);
  const performanceRef = useRef({ frameCount: 0, lastFpsCheck: 0, fps: 60 });
  const previousVrmUrlRef = useRef<string | null>(null);
  const currentAgentIdRef = useRef<string>(agentId);

  // LocalAIVtuber参考: 高度なVRM制御
  const [currentEmotion, setCurrentEmotion] = useState<keyof typeof EMOTION_EXPRESSIONS>('neutral');
  const [blinkTimer, setBlinkTimer] = useState(0);
  const [idleAnimationTime, setIdleAnimationTime] = useState(0);
  const emotionTransitionRef = useRef<Record<string, number>>({});
  const lastBlinkTimeRef = useRef(0);
  const lipSyncSmoothingRef = useRef(0);

  // マークレンダリング要求（最優先で定義）
  const markNeedsRender = useCallback(() => {
    needsRenderRef.current = true;
  }, []);

  // VRMモデルのバウンディングボックスを計算
  const calculateModelBounds = useCallback((modelScene: any) => {
    if (!modelScene) return null;

    const box = new THREE_CACHE.Box3().setFromObject(modelScene);
    const size = box.getSize(new THREE_CACHE.Vector3());
    const center = box.getCenter(new THREE_CACHE.Vector3());

    // VRMの一般的な部位の高さを推定
    const totalHeight = size.y;
    const headTop = center.y + size.y / 2;
    const headBottom = headTop - totalHeight * 0.15; // 頭部は全体の約15%
    const chestTop = headBottom;
    const chestBottom = center.y - size.y * 0.3; // バストアップ範囲をもう少し下に（胸上部まで）
    const footBottom = center.y - size.y / 2;

    console.log(`📏 VRMバウンディング計算:`, {
      totalHeight,
      headTop,
      headBottom,
      chestTop,
      chestBottom,
      footBottom,
      center: { x: center.x, y: center.y, z: center.z },
      size: { x: size.x, y: size.y, z: size.z }
    });

    return {
      box,
      size,
      center,
      totalHeight,
      headTop,
      headBottom,
      chestTop,
      chestBottom,
      footBottom
    };
  }, []);

  // カメラ位置を調整（ビューモードに応じて + ズーム対応）
  const adjustCameraForViewMode = useCallback((bounds: any, mode: 'bust' | 'full', customZoom?: number) => {
    if (!cameraRef.current || !bounds) return;

    const camera = cameraRef.current;
    const { center, totalHeight, headTop, headBottom, chestBottom, footBottom } = bounds;
    const currentZoom = customZoom || zoomRef.current;

    if (mode === 'full') {
      // 全身表示：頭から足まで
      const targetHeight = totalHeight * 1.1; // 少し余裕を持たせる
      const targetCenter = new THREE_CACHE.Vector3(center.x, center.y, center.z);
      
      // カメラ距離を計算（FOVに基づいて + ズーム適用）
      const fov = camera.fov * (Math.PI / 180);
      const baseDistance = targetHeight / (2 * Math.tan(fov / 2)) * 1.2;
      const distance = baseDistance / currentZoom;
      
      camera.position.set(0, targetCenter.y, distance);
      camera.lookAt(targetCenter);
      
      console.log(`📷 全身表示設定 (zoom: ${currentZoom.toFixed(2)}):`, {
        targetHeight,
        baseDistance,
        distance,
        cameraPosition: { x: 0, y: targetCenter.y, z: distance },
        lookAt: targetCenter
      });
    } else {
      // バストアップ表示：顔中心に大きく拡大
      const faceHeight = (headTop - headBottom) * 1.8; // 顔周りの範囲を拡大
      const faceCenter = new THREE_CACHE.Vector3(
        center.x, 
        headBottom + (headTop - headBottom) * 0.3, // 顔の中心より少し下
        center.z
      );
      
      // 顔が見えるレベルまで大幅拡大 + ズーム適用
      const fov = camera.fov * (Math.PI / 180);
      const baseDistance = faceHeight / (2 * Math.tan(fov / 2)) * 0.6;
      const distance = baseDistance / currentZoom;
      
      camera.position.set(0, faceCenter.y, distance);
      camera.lookAt(faceCenter);
      
      console.log(`📷 バストアップ表示設定 (zoom: ${currentZoom.toFixed(2)}):`, {
        faceHeight,
        baseDistance,
        distance,
        cameraPosition: { x: 0, y: faceCenter.y, z: distance },
        lookAt: faceCenter
      });
    }

    markNeedsRender();
  }, [markNeedsRender]);

  // 自然な待機ポーズの定義（T-pose完全解消）
  const NATURAL_IDLE_POSES = {
    arms: { 
      leftArm: -1.5,   // 左腕を大きく下に下ろす
      rightArm: 1.5,   // 右腕を大きく下に下ろす  
      leftForeArm: 0.5, // 肘を少し曲げる
      rightForeArm: -0.5 // 肘を少し曲げる（右は反対方向）
    },
    hands: { leftHand: 0.1, rightHand: 0.1 },
    body: { spine: 0.1, chest: 0.05 }, // より自然な前傾
    head: { neck: 0.05, head: 0.02 }
  };

  // VRM読み込み完了後に自動実行する自然ポーズ設定
  const applyNaturalIdlePose = useCallback((vrm: any) => {
    if (!vrm?.humanoid) return;
    
    try {
      const humanoid = vrm.humanoid;
      console.log('🎭 自然な待機ポーズを適用開始');
      
      // 腕を自然な位置に下ろす（T-pose解消）
      const leftUpperArm = humanoid.getNormalizedBoneNode('leftUpperArm');
      const rightUpperArm = humanoid.getNormalizedBoneNode('rightUpperArm');
      const leftLowerArm = humanoid.getNormalizedBoneNode('leftLowerArm');
      const rightLowerArm = humanoid.getNormalizedBoneNode('rightLowerArm');
      
      if (leftUpperArm) leftUpperArm.rotation.z = NATURAL_IDLE_POSES.arms.leftArm;
      if (rightUpperArm) rightUpperArm.rotation.z = NATURAL_IDLE_POSES.arms.rightArm;
      if (leftLowerArm) leftLowerArm.rotation.y = NATURAL_IDLE_POSES.arms.leftForeArm;
      if (rightLowerArm) rightLowerArm.rotation.y = NATURAL_IDLE_POSES.arms.rightForeArm;
      
      // 軽い前傾姿勢で自然な立ち姿に
      const spine = humanoid.getNormalizedBoneNode('spine');
      if (spine) spine.rotation.x = NATURAL_IDLE_POSES.body.spine;
      
      console.log('🎭 自然な待機ポーズを適用完了');
    } catch (error) {
      console.warn('⚠️ 自然ポーズ適用エラー:', error);
    }
  }, []);

  // ポーズ状態の保存・復元（記憶機能）
  const saveModelState = useCallback((agentId: string, vrm: any) => {
    if (!vrm?.humanoid) return;
    
    try {
      const poseState = {
        lastPose: 'natural_idle',
        boneRotations: {} as any,
        expressions: {} as any,
        timestamp: Date.now()
      };
      
      // 主要ボーンの回転状態を保存
      ['leftUpperArm', 'rightUpperArm', 'leftLowerArm', 'rightLowerArm', 'spine', 'head'].forEach(boneName => {
        const bone = vrm.humanoid.getNormalizedBoneNode(boneName);
        if (bone) {
          poseState.boneRotations[boneName] = {
            x: bone.rotation.x,
            y: bone.rotation.y,
            z: bone.rotation.z
          };
        }
      });
      
      localStorage.setItem(`vrm-pose-${agentId}`, JSON.stringify(poseState));
      console.log(`💾 ${agentId}のポーズ状態を保存完了`);
    } catch (error) {
      console.warn('ポーズ状態保存エラー:', error);
    }
  }, []);

  const restoreModelState = useCallback((agentId: string, vrm: any) => {
    const savedState = localStorage.getItem(`vrm-pose-${agentId}`);
    if (!savedState || !vrm?.humanoid) return false;
    
    try {
      const poseState = JSON.parse(savedState);
      
      // 保存された回転状態を復元
      Object.entries(poseState.boneRotations).forEach(([boneName, rotation]: [string, any]) => {
        const bone = vrm.humanoid.getNormalizedBoneNode(boneName);
        if (bone) {
          bone.rotation.set(rotation.x, rotation.y, rotation.z);
        }
      });
      
      console.log(`🔄 ${agentId}のポーズ状態を復元: ${poseState.lastPose}`);
      return true;
    } catch (error) {
      console.warn('ポーズ状態復元エラー:', error);
      return false;
    }
  }, []);

  // 統一モデルシステムの初期化
  const initializeUniversalSystem = useCallback(async () => {
    if (isUniversalSystemInitialized.current) return;

    try {
      console.log('🔧 統一モデルシステム初期化開始');

      // VRMローダープラグインを登録
      const vrmPlugin = new VRMLoaderPlugin();
      universalModelLoader.registerPlugin(vrmPlugin);

      isUniversalSystemInitialized.current = true;
      console.log('✅ 統一モデルシステム初期化完了');

    } catch (error) {
      console.error('❌ 統一モデルシステム初期化エラー:', error);
    }
  }, []);

  // 統一システムでVRMを読み込み
  const loadVRMWithUniversalSystem = useCallback(async (data: ArrayBuffer, filename: string) => {
    try {
      console.log(`🚀 統一システムでVRM読み込み開始: ${filename}`);

      // 統一ローダーでモデルを読み込み
      const result = await universalModelLoader.loadModel(data, filename);

      if (!result.success || !result.model) {
        throw new Error(result.error || 'モデル読み込みに失敗');
      }

      const model = result.model;
      universalModelRef.current = model;

      // モデルコントローラーを作成
      if (sceneRef.current) {
        const controller = new UniversalModelController(model, sceneRef.current);
        modelControllerRef.current = controller;

        // イベントリスナーを設定
        controller.addEventListener('initialized', () => {
          console.log('🎮 モデルコントローラー初期化完了');
          setModelLoaded(true);
          markNeedsRender();
        });

        controller.addEventListener('expressionChanged', (event) => {
          console.log('😊 表情変更:', event.data);
        });
      }

      // 従来のvrmRefも設定（互換性のため）
      vrmRef.current = {
        vrm: model.data.vrm,
        scene: model.data.scene,
        arrayBufferSize: model.fileSize
      };

      console.log(`✅ 統一システムでVRM読み込み完了: ${model.name}`);
      return model;

    } catch (error) {
      console.error('❌ 統一システムVRM読み込みエラー:', error);
      throw error;
    }
  }, [markNeedsRender]);

  // LocalAIVtuber参考: 感情表現制御
  const updateVRMExpression = useCallback((emotion: keyof typeof EMOTION_EXPRESSIONS, intensity: number = 1.0) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;
      const targetExpressions = EMOTION_EXPRESSIONS[emotion];

      // 現在の表情から目標表情へのスムーズな遷移
      Object.entries(targetExpressions).forEach(([expressionName, targetValue]) => {
        const currentValue = emotionTransitionRef.current[expressionName] || 0;
        const newValue = currentValue + (targetValue * intensity - currentValue) * VRM_CONTROL_CONFIG.EMOTION_TRANSITION_SPEED;

        emotionTransitionRef.current[expressionName] = newValue;
        expressionManager.setValue(expressionName, newValue);
      });

      expressionManager.update();
      onExpressionChange?.(emotion, intensity);
      markNeedsRender();

    } catch (error) {
      console.warn('VRM表情更新エラー:', error);
    }
  }, [markNeedsRender, onExpressionChange]);

  // LocalAIVtuber参考: リップシンク制御
  const updateLipSync = useCallback((volume: number) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;

      // 音量に基づく口の開き具合（スムージング適用）
      const targetMouthOpen = Math.min(volume * VRM_CONTROL_CONFIG.MOUTH_SYNC_SENSITIVITY, 1.0);
      lipSyncSmoothingRef.current += (targetMouthOpen - lipSyncSmoothingRef.current) * VRM_CONTROL_CONFIG.LIP_SYNC_SMOOTHING;

      // VRM標準の口形状を適用
      expressionManager.setValue('aa', lipSyncSmoothingRef.current * 0.8);
      expressionManager.setValue('ih', lipSyncSmoothingRef.current * 0.3);
      expressionManager.setValue('ou', lipSyncSmoothingRef.current * 0.5);
      expressionManager.setValue('ee', lipSyncSmoothingRef.current * 0.2);
      expressionManager.setValue('oh', lipSyncSmoothingRef.current * 0.6);

      expressionManager.update();
      markNeedsRender();

    } catch (error) {
      console.warn('VRMリップシンクエラー:', error);
    }
  }, [markNeedsRender]);

  // フェイストラッキングデータをVRM表情に適用
  const applyFaceTrackingToVRM = useCallback((trackingData: FaceTrackingData) => {
    if (!vrmRef.current?.vrm?.expressionManager) return;

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;
      
      // 基本表情の適用
      expressionManager.setValue('happy', trackingData.happy * 0.8);
      expressionManager.setValue('angry', trackingData.angry * 0.8);
      expressionManager.setValue('sad', trackingData.sad * 0.8);
      expressionManager.setValue('relaxed', trackingData.relaxed * 0.8);
      expressionManager.setValue('surprised', trackingData.surprised * 0.8);
      
      // まばたき制御（フェイストラッキング優先）
      if (trackingData.eyeBlinkLeft > 0.1 || trackingData.eyeBlinkRight > 0.1) {
        expressionManager.setValue('blink', (trackingData.eyeBlinkLeft + trackingData.eyeBlinkRight) / 2);
        expressionManager.setValue('blinkLeft', trackingData.eyeBlinkLeft);
        expressionManager.setValue('blinkRight', trackingData.eyeBlinkRight);
      }
      
      // 口の形（リップシンク）
      expressionManager.setValue('aa', trackingData.aa * 0.9);
      expressionManager.setValue('ih', trackingData.ih * 0.9);
      expressionManager.setValue('ou', trackingData.ou * 0.9);
      expressionManager.setValue('ee', trackingData.ee * 0.9);
      expressionManager.setValue('oh', trackingData.oh * 0.9);
      
      // 頭部回転の適用（VRMhumanoidへの適用）
      if (vrmRef.current.vrm.humanoid) {
        const humanoid = vrmRef.current.vrm.humanoid;
        const head = humanoid.getNormalizedBoneNode('head');
        
        if (head) {
          // MediaPipeのヘッド回転をVRM座標系に適用
          head.rotation.x = trackingData.headRotationX * 0.5; // ピッチ
          head.rotation.y = trackingData.headRotationY * 0.5; // ヨー
          head.rotation.z = trackingData.headRotationZ * 0.3; // ロール
        }
      }
      
      // 表情を更新
      expressionManager.update();
      markNeedsRender();
      
      console.log('🎭 フェイストラッキング → VRM表情適用完了', {
        happy: trackingData.happy.toFixed(2),
        angry: trackingData.angry.toFixed(2),
        headY: trackingData.headRotationY.toFixed(2)
      });
      
    } catch (error) {
      console.warn('⚠️ フェイストラッキング適用エラー:', error);
    }
  }, [markNeedsRender]);

  // フェイストラッキングデータ監視
  useEffect(() => {
    if (faceTrackingData) {
      applyFaceTrackingToVRM(faceTrackingData);
    }
  }, [faceTrackingData, applyFaceTrackingToVRM]);

  // LocalAIVtuber参考: 自動まばたき（点滅修正）
  const updateAutoBlinking = useCallback((deltaTime: number) => {
    // フェイストラッキングが有効な場合は自動まばたきを無効化
    if (!enableAutoBlinking || !vrmRef.current?.vrm?.expressionManager || faceTrackingData) return;

    const now = Date.now();

    try {
      const expressionManager = vrmRef.current.vrm.expressionManager;

      // まばたき開始判定
      if (blinkTimer === 0 && now - lastBlinkTimeRef.current > VRM_CONTROL_CONFIG.BLINK_FREQUENCY + Math.random() * 2000) {
        setBlinkTimer(0.01); // まばたき開始
      }

      // まばたきアニメーション実行中
      if (blinkTimer > 0) {
        const blinkProgress = blinkTimer / 0.3; // 0.3秒でまばたき完了
        let blinkIntensity = 0;

        if (blinkProgress < 0.5) {
          // 閉じる（0→1）
          blinkIntensity = Math.sin(blinkProgress * Math.PI);
        } else {
          // 開く（1→0）
          blinkIntensity = Math.sin((1 - blinkProgress) * Math.PI);
        }

        // まばたき表情を適用
        expressionManager.setValue('blink', blinkIntensity);
        expressionManager.setValue('blinkLeft', blinkIntensity);
        expressionManager.setValue('blinkRight', blinkIntensity);

        setBlinkTimer(prev => prev + deltaTime);

        if (blinkTimer >= 0.3) { // まばたき完了
          setBlinkTimer(0);
          lastBlinkTimeRef.current = now;

          // まばたき表情をリセット
          expressionManager.setValue('blink', 0);
          expressionManager.setValue('blinkLeft', 0);
          expressionManager.setValue('blinkRight', 0);
        }
      }

    } catch (error) {
      console.warn('VRM自動まばたきエラー:', error);
    }
  }, [enableAutoBlinking, blinkTimer]);

  // LocalAIVtuber参考: アイドルアニメーション
  const updateIdleAnimation = useCallback((deltaTime: number) => {
    if (!enableIdleAnimation || !vrmRef.current?.vrm?.humanoid) return;

    try {
      const humanoid = vrmRef.current.vrm.humanoid;
      const time = idleAnimationTime * VRM_CONTROL_CONFIG.IDLE_ANIMATION_SPEED;

      // 軽微な呼吸アニメーション
      const breathingIntensity = Math.sin(time * 2) * 0.02;
      const chestBone = humanoid.getNormalizedBoneNode('chest');
      if (chestBone) {
        chestBone.rotation.x = breathingIntensity;
      }

      // 軽微な頭の動き
      const headSway = Math.sin(time * 0.5) * 0.01;
      const headBone = humanoid.getNormalizedBoneNode('head');
      if (headBone) {
        headBone.rotation.y = headSway;
      }

      setIdleAnimationTime(prev => prev + deltaTime);
      markNeedsRender();

    } catch (error) {
      console.warn('VRMアイドルアニメーションエラー:', error);
    }
  }, [enableIdleAnimation, idleAnimationTime, markNeedsRender]);


  // テクスチャ圧縮とロード最適化
  const optimizeTexture = useCallback((texture: any, THREE: any) => {
    if (!texture) return texture;
    
    // テクスチャサイズ制限
    const maxSize = PERFORMANCE_CONFIG.MAX_TEXTURE_SIZE;
    if (texture.image) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx && (texture.image.width > maxSize || texture.image.height > maxSize)) {
        const scale = Math.min(maxSize / texture.image.width, maxSize / texture.image.height);
        canvas.width = texture.image.width * scale;
        canvas.height = texture.image.height * scale;
        ctx.drawImage(texture.image, 0, 0, canvas.width, canvas.height);
        texture.image = canvas;
      }
    }
    
    texture.generateMipmaps = true;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.format = THREE.RGBAFormat;
    texture.flipY = false;
    
    return texture;
  }, []);

  // ジオメトリ最適化
  const optimizeGeometry = useCallback((geometry: any) => {
    if (!geometry) return geometry;
    
    const unnecessaryAttributes = ['uv2', 'uv3', 'color2'];
    unnecessaryAttributes.forEach(attr => {
      if (geometry.attributes[attr]) {
        geometry.deleteAttribute(attr);
      }
    });
    
    return geometry;
  }, []);

  // モデルクリア関数（安定化）
  const clearCurrentModel = useCallback(() => {
    if (vrmRef.current && sceneRef.current) {
      try {
        // シーンからモデルを安全に削除
        if (vrmRef.current.scene && vrmRef.current.scene.parent) {
          sceneRef.current.remove(vrmRef.current.scene);
        }
        
        // モデルのリソース解放
        if (vrmRef.current.scene) {
          vrmRef.current.scene.traverse((object: any) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((mat: any) => {
                  if (mat.map) mat.map.dispose();
                  if (mat.normalMap) mat.normalMap.dispose();
                  if (mat.roughnessMap) mat.roughnessMap.dispose();
                  if (mat.metalnessMap) mat.metalnessMap.dispose();
                  mat.dispose();
                });
              } else {
                if (object.material.map) object.material.map.dispose();
                if (object.material.normalMap) object.material.normalMap.dispose();
                if (object.material.roughnessMap) object.material.roughnessMap.dispose();
                if (object.material.metalnessMap) object.material.metalnessMap.dispose();
                object.material.dispose();
              }
            }
          });
        }
        
        vrmRef.current = null;
        setModelLoaded(false);
        markNeedsRender();
        console.log(`🧹 VRMモデルをクリア: ${agentId}`);
      } catch (error) {
        console.error(`⚠️ モデルクリア中にエラー: ${agentId}`, error);
        vrmRef.current = null;
        setModelLoaded(false);
      }
    }
  }, [markNeedsRender, agentId]);

  // VRMファイルのバージョン検証関数
  const detectVRMVersion = useCallback((arrayBuffer: ArrayBuffer): 'vrm0' | 'vrm1' | 'gltf2' | 'unknown' => {
    try {
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        console.error(`❌ ArrayBufferが空: ${agentId}`);
        return 'unknown';
      }

      const uint8Array = new Uint8Array(arrayBuffer, 0, Math.min(1024, arrayBuffer.byteLength));
      
      // バイナリglTFファイルかチェック（最初の4バイトが"glTF"）
      if (uint8Array[0] === 0x67 && uint8Array[1] === 0x6C && uint8Array[2] === 0x54 && uint8Array[3] === 0x46) {
        console.log(`📦 バイナリglTF形式検出: ${agentId}`);
        return 'gltf2'; // バイナリはたいていglTF 2.0
      }

      // JSONテキストとして解析
      const jsonString = new TextDecoder().decode(uint8Array);
      console.log(`🔍 VRMバージョン検出: ${agentId} - ファイルサイズ: ${arrayBuffer.byteLength} bytes`);
      
      // JSONが含まれているかチェック
      if (jsonString.includes('"asset"')) {
        if (jsonString.includes('"version":"2.') || jsonString.includes('"version": "2.')) {
          if (jsonString.includes('"VRM"') || jsonString.includes('"VRMC_')) {
            console.log(`✅ VRM 1.0 (glTF 2.0ベース) 検出: ${agentId}`);
            return 'vrm1';
          } else {
            console.log(`✅ 標準glTF 2.0 検出: ${agentId}`);
            return 'gltf2';
          }
        } else if (jsonString.includes('"version":"1.') || jsonString.includes('"version": "1.')) {
          console.log(`✅ VRM 0.x (glTF 1.0ベース) 検出: ${agentId}`);
          return 'vrm0';
        }
      }
      
      console.warn(`⚠️ VRMバージョン不明: ${agentId} - JSONヘッダーが見つからない`);
      return 'unknown';
    } catch (error) {
      console.error(`❌ VRMバージョン検出エラー: ${agentId}`, error);
      return 'unknown';
    }
  }, [agentId]);

  // VRM読み込み関数（改善版・aituber-kit参考）
  const loadVRM = useCallback(async (retryCount = 0) => {
    if ((!vrmUrl && !vrmArrayBuffer) || loadingRef.current) {
      console.log(`⏸️ VRM読み込みスキップ: ${agentId} - データなし または 読み込み中`);
      return;
    }

    if (vrmArrayBuffer && !vrmArrayBuffer.byteLength) {
      console.error(`❌ VRM読み込み中止: ${agentId} - ArrayBufferが無効`);
      setError('VRMファイルが正しく読み込まれていません');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      loadingRef.current = true;

      console.log(`🔄 VRM読み込み開始: ${agentId}`);
      console.log(`📋 データ種類: ${vrmArrayBuffer ? `ArrayBuffer (${vrmArrayBuffer.byteLength} bytes)` : `URL: ${vrmUrl}`}`);

      // 既存のモデルがある場合は先にクリア
      if (vrmRef.current) {
        console.log(`🧹 既存モデルをクリア: ${agentId}`);
        clearCurrentModel();
      }

      // 統一システムでの読み込みを試行（ArrayBufferの場合のみ）
      if (vrmArrayBuffer && isUniversalSystemInitialized.current) {
        try {
          console.log(`🚀 統一システムでVRM読み込み試行: ${agentId}`);
          const universalModel = await loadVRMWithUniversalSystem(vrmArrayBuffer, `${agentId}.vrm`);

          if (universalModel && universalModel.data.scene) {
            console.log(`✅ 統一システムでVRM読み込み成功: ${agentId}`);
            setModelLoaded(true);
            markNeedsRender();
            return; // 成功したら従来の処理をスキップ
          }
        } catch (error) {
          console.warn(`⚠️ 統一システムでの読み込み失敗、従来方式にフォールバック: ${agentId}`, error);
          // エラーの場合は従来の処理を続行
        }
      }

      // VRMバージョン検証（ArrayBufferの場合のみ）
      let vrmVersion = 'unknown';
      if (vrmArrayBuffer) {
        vrmVersion = detectVRMVersion(vrmArrayBuffer);
        console.log(`📋 検出されたVRMバージョン: ${vrmVersion}`);

        // VRM 0.x形式の場合は警告表示（最新ローダーで試行）
        if (vrmVersion === 'vrm0') {
          console.warn(`⚠️ VRM 0.x形式検出 - 最新ローダーで読み込み試行: ${agentId}`);
          console.warn(`📋 注意: VRM 1.0形式への変換を推奨します`);
        }
      }

      // キャッシュされたライブラリを使用
      const THREE = THREE_CACHE || await import('three');
      if (!THREE_CACHE) THREE_CACHE = THREE;
      
      const GLTFLoaderModule = GLTFLoader_CACHE || (await import('three/examples/jsm/loaders/GLTFLoader.js')).GLTFLoader;
      if (!GLTFLoader_CACHE) GLTFLoader_CACHE = GLTFLoaderModule;
      
      // 最新のVRMローダーを使用（全バージョン対応・互換性強化）
      console.log(`📦 VRMローダーを初期化: ${agentId}`);
      const vrmCurrent = VRMLoader_CACHE || await import('@pixiv/three-vrm');
      const VRMLoaderPlugin = vrmCurrent.VRMLoaderPlugin;
      const VRMUtils = vrmCurrent.VRMUtils;
      if (!VRMLoader_CACHE) VRMLoader_CACHE = vrmCurrent;

      const gltfLoader = new GLTFLoaderModule();

      // VRMローダープラグインを設定（エラーハンドリング強化）
      try {
        gltfLoader.register((parser) => {
          const vrmPlugin = new VRMLoaderPlugin(parser);
          console.log(`🔧 VRMローダープラグイン登録: ${agentId}`);
          return vrmPlugin;
        });
      } catch (error) {
        console.warn(`⚠️ VRMローダープラグイン登録警告: ${agentId}`, error);
        // フォールバック: プラグインなしでglTFとして読み込み
      }
      
      console.log(`🎮 VRMローダー開始: ${agentId}`);
      
      let gltfData;
      
      if (vrmArrayBuffer) {
        // ArrayBufferの場合：aituber-kit方式でBlob経由読み込み
        console.log(`📦 ArrayBuffer Blob経由読み込み: ${agentId} (${vrmArrayBuffer.byteLength} bytes)`);
        console.log(`📋 ArrayBuffer型確認:`, vrmArrayBuffer.constructor.name);
        console.log(`📋 ArrayBufferの最初の8バイト:`, new Uint8Array(vrmArrayBuffer, 0, 8));

        // aituber-kit方式: Blobを経由してURLを作成（より安定）
        const blob = new Blob([vrmArrayBuffer], { type: 'application/octet-stream' });
        const blobUrl = window.URL.createObjectURL(blob);

        try {
          gltfData = await new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              reject(new Error(`VRM読み込みタイムアウト (30秒): ${agentId}`));
            }, 30000);

            gltfLoader.load(
              blobUrl,
              (gltf) => {
                clearTimeout(timeoutId);
                console.log(`✅ Blob VRM読み込み成功: ${agentId}`, gltf);
                console.log(`📋 gltf.scene:`, gltf.scene);
                console.log(`📋 gltf.userData:`, gltf.userData);
                console.log(`📋 gltf.userData.vrm:`, gltf.userData?.vrm);
                resolve(gltf);
              },
              (progress) => {
                const percent = Math.round((progress.loaded / progress.total) * 100);
                console.log(`📈 VRM読み込み進捗 (${agentId}): ${percent}%`);
              },
              (error) => {
                clearTimeout(timeoutId);
                console.error(`❌ Blob VRM読み込みエラー: ${agentId}`, error);
                reject(new Error(`VRMファイル読み込み失敗: ${error.message || 'Blob VRMローダーエラー'}`));
              }
            );
          });
        } finally {
          // 使用後にBlobURLを解放（メモリリーク防止）
          window.URL.revokeObjectURL(blobUrl);
        }
      } else if (vrmUrl?.startsWith('http')) {
        // HTTP URLの場合：通常のload
        console.log(`🌐 HTTP URL読み込み: ${agentId} - ${vrmUrl}`);
        gltfData = await new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error(`VRM読み込みタイムアウト (30秒): ${agentId}`));
          }, 30000);

          gltfLoader.load(
            vrmUrl,
            (gltf) => {
              clearTimeout(timeoutId);
              console.log(`✅ HTTP VRM読み込み成功: ${agentId}`, gltf);
              resolve(gltf);
            },
            (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              console.log(`📈 VRM読み込み進捗 (${agentId}): ${percent}%`);
            },
            (error) => {
              clearTimeout(timeoutId);
              console.error(`❌ HTTP VRM読み込みエラー: ${agentId}`, error);
              reject(new Error(`VRMファイル解析失敗: ${error.message || 'HTTP VRMローダーエラー'}`));
            }
          );
        });
      } else {
        throw new Error('無効なデータです（ArrayBufferまたはHTTP URLが必要）');
      }

      // VRMデータを抽出（glTF互換性向上）
      const vrm = gltfData.userData?.vrm;
      const scene = gltfData.scene;

      if (vrm) {
        console.log(`🎭 VRM固有データ検出: ${agentId}`, vrm.meta?.title || 'Unknown VRM');
      } else {
        console.log(`📦 標準glTF形式として読み込み: ${agentId}`);
        console.log(`📋 glTFシーン情報:`, {
          children: scene?.children?.length || 0,
          animations: gltfData.animations?.length || 0,
          materials: gltfData.materials?.length || 0,
          meshes: gltfData.meshes?.length || 0
        });
      }

      // シーンに追加
      if (sceneRef.current && scene) {
        console.log(`🎯 シーンへの追加開始: ${agentId}`);
        console.log(`📋 scene.children.length:`, scene.children.length);
        console.log(`📋 sceneRef.current:`, sceneRef.current);
        
        // モデルの最適化とスケーリング
        scene.scale.set(1, 1, 1);
        scene.position.set(0, -1, 0);
        scene.rotation.y = Math.PI; // 180度回転（VRMは元々後ろ向きのため）
        
        console.log(`🔧 シーンのスケール・位置・回転設定完了: ${agentId}`);
        
        // マテリアルとテクスチャの最適化
        let meshCount = 0;
        scene.traverse((object: any) => {
          if (object.isMesh) {
            meshCount++;
            optimizeGeometry(object.geometry);
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((mat: any) => {
                  if (mat.map) optimizeTexture(mat.map, THREE);
                  if (mat.normalMap) optimizeTexture(mat.normalMap, THREE);
                });
              } else {
                if (object.material.map) optimizeTexture(object.material.map, THREE);
                if (object.material.normalMap) optimizeTexture(object.material.normalMap, THREE);
              }
            }
          }
        });
        
        console.log(`🔧 マテリアル最適化完了: ${agentId} (${meshCount}個のメッシュ)`);

        sceneRef.current.add(scene);
        vrmRef.current = { scene, vrm, gltf: gltfData };
        setModelLoaded(true);

        // VRMモデルのバウンディングボックスを計算してカメラ位置を最適化
        const bounds = calculateModelBounds(scene);
        if (bounds) {
          adjustCameraForViewMode(bounds, viewMode, zoomRef.current);
        }

        // 強制的にレンダリングを実行（表示確保）
        markNeedsRender();
        setTimeout(() => markNeedsRender(), 100);
        setTimeout(() => markNeedsRender(), 500);

        console.log(`✅ VRM読み込み完了: ${agentId} - ${scene.name || vrm?.meta?.title || 'VRM Model'}`);
        console.log(`📋 最終確認 - sceneRef.current.children.length:`, sceneRef.current.children.length);
        console.log(`📋 最終確認 - modelLoaded:`, true);

        // 自然な初期ポーズ適用（T-pose解消）
        setTimeout(() => {
          if (vrmRef.current?.vrm) {
            const restored = restoreModelState(agentId, vrmRef.current.vrm);
            if (!restored) {
              applyNaturalIdlePose(vrmRef.current.vrm);
            }
            saveModelState(agentId, vrmRef.current.vrm);
            markNeedsRender();
          }
        }, 500);
      } else {
        console.error(`❌ シーン追加失敗: ${agentId}`);
        console.error(`📋 sceneRef.current:`, sceneRef.current);
        console.error(`📋 scene:`, scene);
      }
    } catch (error: any) {
      console.error(`⚠️ VRM読み込み失敗: ${agentId}`, error);
      
      // エラー内容に応じた詳細メッセージ
      let errorMessage = 'VRMファイルの読み込みに失敗しました';
      
      if (error.message?.includes('Unsupported asset')) {
        if (error.message?.includes('glTF versions >=2.0')) {
          errorMessage = '⚠️ VRM 0.x形式（glTF 1.0）の互換性問題\n\n' + 
                       '📋 解決方法:\n' +
                       '• VRoidStudio最新版でVRM 1.0形式に変換\n' +
                       '• Blender VRMアドオンで形式を更新\n' +
                       '• UniVRMで再エクスポート\n\n' +
                       '💡 VRM 1.0形式は安定性・互換性が向上しています';
        } else {
          errorMessage = '⚠️ 未対応のファイル形式\n\n対応形式: VRM 0.x/1.0, glTF 2.0';
        }
      } else if (error.message?.includes('Failed to fetch')) {
        errorMessage = '📡 ネットワークエラー\n\nファイルの取得に失敗しました';
      } else if (error.message?.includes('parse') || error.message?.includes('JSON')) {
        errorMessage = '🔧 ファイル解析エラー\n\n' +
                       '原因:\n' +
                       '• ファイルが破損している可能性\n' +
                       '• 不完全なアップロード\n' +
                       '• 非VRMファイルの選択\n\n' +
                       '💡 再度ファイルをアップロードしてみてください';
      } else if (error.message?.includes('timeout')) {
        errorMessage = '⏱️ 読み込みタイムアウト\n\nファイルサイズが大きすぎます（推奨: 50MB未満）';
      }
      
      setError(errorMessage);
      
      // リトライは無効化（無限ループ防止）
      console.log(`🚫 VRM読み込み失敗: ${agentId} - リトライなし`);
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [vrmUrl, vrmArrayBuffer, agentId, markNeedsRender, clearCurrentModel, optimizeGeometry, optimizeTexture, detectVRMVersion]);

  // プレースホルダー表示関数（非表示）
  const showPlaceholder = useCallback(() => {
    console.log(`📦 プレースホルダー非表示: ${agentId}`);
    clearCurrentModel();
    // 何も表示しない
  }, [agentId]);

  // エージェント変更時の処理
  useEffect(() => {
    if (currentAgentIdRef.current !== agentId) {
      console.log(`🔄 エージェント変更: ${currentAgentIdRef.current} → ${agentId}`);
      console.log(`📋 変更前URL: ${previousVrmUrlRef.current}`);
      console.log(`📋 変更後URL: ${vrmUrl}`);
      
      // 前のエージェントのモデルを確実にクリア
      clearCurrentModel();
      
      currentAgentIdRef.current = agentId;
      previousVrmUrlRef.current = null; // エージェント変更時はURLをリセット
      
      console.log(`✅ エージェント変更処理完了: ${agentId}`);
    }
  }, [agentId, clearCurrentModel, vrmUrl]);


  // ビューモード変更時のカメラ調整
  useEffect(() => {
    if (modelLoaded && vrmRef.current?.scene) {
      const bounds = calculateModelBounds(vrmRef.current.scene);
      if (bounds) {
        adjustCameraForViewMode(bounds, viewMode, zoomRef.current);
      }
    }
  }, [viewMode, modelLoaded, calculateModelBounds, adjustCameraForViewMode]);

  // 強制レンダリング処理
  useEffect(() => {
    if (forceRender && modelLoaded) {
      markNeedsRender();
      if (onRenderComplete) {
        onRenderComplete();
      }
    }
  }, [forceRender, modelLoaded, markNeedsRender, onRenderComplete]);

  // VRM データ変更時の処理（状態リセット問題修正版）
  const arrayBufferRef = useRef<ArrayBuffer | null>(null);
  const arrayBufferSizeRef = useRef<number>(0);
  
  useEffect(() => {
    if (!sceneRef.current || !THREE_CACHE) return;

    // より厳密な変更検出（不要な再読み込み防止）
    const arrayBufferChanged = vrmArrayBuffer !== arrayBufferRef.current;
    const arraySizeChanged = vrmArrayBuffer?.byteLength !== arrayBufferSizeRef.current;
    const urlChanged = vrmUrl !== previousVrmUrlRef.current;
    const agentChanged = agentId !== currentAgentIdRef.current;

    console.log(`🔄 VRMデータ確認: ${agentId}`, {
      arrayBufferChanged,
      arraySizeChanged,
      urlChanged,
      agentChanged,
      arrayBufferSize: vrmArrayBuffer?.byteLength || 0,
      previousSize: arrayBufferSizeRef.current
    });

    // 変更が実際にあった場合のみ処理（状態リセット問題解消）
    if (arrayBufferChanged || arraySizeChanged || urlChanged || agentChanged) {
      // 参照を更新
      arrayBufferRef.current = vrmArrayBuffer || null;
      arrayBufferSizeRef.current = vrmArrayBuffer?.byteLength || 0;
      previousVrmUrlRef.current = vrmUrl || null;
      currentAgentIdRef.current = agentId;

      console.log(`🔄 VRMデータ変更検出: ${agentId}`);

      if (vrmArrayBuffer || vrmUrl) {
        // データがある場合は読み込み
        const loadTimer = setTimeout(() => {
          loadVRM();
        }, 100);
        return () => clearTimeout(loadTimer);
      } else {
        // データがない場合はクリア
        console.log(`🗑️ VRM削除検出: ${agentId}`);
        clearCurrentModel();
        showPlaceholder();
      }
    }
  }, [vrmUrl, vrmArrayBuffer, agentId]); // 依存配列を最小限に

  // キャンバスリサイズ検出
  useEffect(() => {
    if (!canvasRef.current || !rendererRef.current || !cameraRef.current) return;

    const canvas = canvasRef.current;
    const renderer = rendererRef.current;
    const camera = cameraRef.current;

    const handleResize = () => {
      const width = canvas.clientWidth;
      const height = canvas.clientHeight;
      
      // レンダラーサイズ更新
      renderer.setSize(width, height);
      
      // カメラアスペクト比更新
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      
      // 強制再レンダリング
      markNeedsRender();
      
      console.log(`🔄 VRMキャンバスリサイズ: ${width}x${height} (${agentId})`);
    };

    // ResizeObserver使用
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.disconnect();
    };
  }, [agentId, markNeedsRender]);

  // マウスホイールによるズーム制御
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;

    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();
      
      const zoomSpeed = 0.1;
      const deltaY = event.deltaY;
      
      // ズームレベルの更新（0.2～5.0の範囲）
      const newZoom = Math.max(0.2, Math.min(5.0, zoomRef.current + (deltaY > 0 ? -zoomSpeed : zoomSpeed)));
      
      if (newZoom !== zoomRef.current) {
        zoomRef.current = newZoom;
        setZoomLevel(newZoom);
        
        // カメラ位置をすぐに更新
        if (modelLoaded && vrmRef.current?.scene) {
          const bounds = calculateModelBounds(vrmRef.current.scene);
          if (bounds) {
            adjustCameraForViewMode(bounds, viewMode, newZoom);
          }
        }
        
        // ズーム設定をlocalStorageに保存
        if (typeof window !== 'undefined') {
          localStorage.setItem(`vrm-zoom-${agentId}`, newZoom.toString());
        }
      }
    };

    canvas.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
    };
  }, [agentId, modelLoaded, viewMode, calculateModelBounds, adjustCameraForViewMode]);

  // VRMモデル位置管理用ref
  const modelPositionRef = useRef({ x: 0, y: 0 });

  // マウスドラッグによるVRMモデル移動制御
  useEffect(() => {
    if (!canvasRef.current || !sceneRef.current || !cameraRef.current) return;

    const canvas = canvasRef.current;
    const camera = cameraRef.current;
    let isDragging = false;
    let previousMousePosition = { x: 0, y: 0 };

    const handleMouseDown = (event: MouseEvent) => {
      // 左クリックでドラッグ開始
      if (event.button === 0) {
        isDragging = true;
        previousMousePosition = {
          x: event.clientX,
          y: event.clientY
        };
        canvas.style.cursor = 'grabbing';
        event.preventDefault();
      }
    };

    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging || !vrmRef.current?.scene) return;

      const deltaX = event.clientX - previousMousePosition.x;
      const deltaY = event.clientY - previousMousePosition.y;
      
      // マウス移動量を3Dワールド座標に変換（感度調整）
      const sensitivity = 0.005;
      const moveX = deltaX * sensitivity;
      const moveY = -deltaY * sensitivity; // Y軸は反転

      // モデル位置を更新（ref使用）
      modelPositionRef.current.x += moveX;
      modelPositionRef.current.y += moveY;

      // VRMモデルの位置を移動
      vrmRef.current.scene.position.x = modelPositionRef.current.x;
      vrmRef.current.scene.position.y = modelPositionRef.current.y;

      previousMousePosition = {
        x: event.clientX,
        y: event.clientY
      };

      markNeedsRender();
    };

    const handleMouseUp = () => {
      if (isDragging) {
        isDragging = false;
        canvas.style.cursor = 'grab';
        
        // 位置をlocalStorageに保存
        if (typeof window !== 'undefined') {
          localStorage.setItem(`vrm-position-${agentId}`, JSON.stringify(modelPositionRef.current));
        }
      }
    };

    // マウスがcanvasから離れた場合もドラッグ終了
    const handleMouseLeave = () => {
      if (isDragging) {
        isDragging = false;
        canvas.style.cursor = 'grab';
      }
    };

    // 保存された位置を復元
    if (typeof window !== 'undefined' && vrmRef.current?.scene) {
      const savedPosition = localStorage.getItem(`vrm-position-${agentId}`);
      if (savedPosition) {
        try {
          const position = JSON.parse(savedPosition);
          modelPositionRef.current = position;
          vrmRef.current.scene.position.x = modelPositionRef.current.x;
          vrmRef.current.scene.position.y = modelPositionRef.current.y;
          markNeedsRender();
          console.log('🔄 VRM位置復元完了:', modelPositionRef.current);
        } catch (error) {
          console.warn('VRM位置復元エラー:', error);
        }
      }
    }

    canvas.style.cursor = 'grab';
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
      canvas.style.cursor = 'default';
    };
  }, [agentId, modelLoaded, markNeedsRender]);

  // ズーム設定の復元
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedZoom = localStorage.getItem(`vrm-zoom-${agentId}`);
      if (savedZoom) {
        const zoom = parseFloat(savedZoom);
        if (zoom >= 0.2 && zoom <= 5.0) {
          zoomRef.current = zoom;
          setZoomLevel(zoom);
        }
      }
    }
  }, [agentId]);

  // Three.js初期化
  useEffect(() => {
    const initThreeJS = async () => {
      try {
        if (!canvasRef.current) return;

        // 統一モデルシステムを初期化
        await initializeUniversalSystem();

        // Three.js動的インポート（キャッシュ活用）
        const THREE = THREE_CACHE || await import('three');
        if (!THREE_CACHE) THREE_CACHE = THREE;

        // シーン初期化
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222222);
        sceneRef.current = scene;

        // カメラ設定
        const camera = new THREE.PerspectiveCamera(
          50, 
          canvasRef.current.clientWidth / canvasRef.current.clientHeight, 
          0.1, 
          1000
        );
        camera.position.set(0, 1.6, 2);
        camera.lookAt(0, 1, 0);
        cameraRef.current = camera;

        // レンダラー設定
        const renderer = new THREE.WebGLRenderer({ 
          canvas: canvasRef.current,
          antialias: true,
          alpha: true
        });
        
        const canvasWidth = canvasRef.current.clientWidth;
        const canvasHeight = canvasRef.current.clientHeight;
        
        console.log(`🖥️ キャンバスサイズ設定: ${agentId}`);
        console.log(`📋 Canvas clientWidth: ${canvasWidth}px`);
        console.log(`📋 Canvas clientHeight: ${canvasHeight}px`);
        console.log(`📋 Canvas offsetWidth: ${canvasRef.current.offsetWidth}px`);
        console.log(`📋 Canvas offsetHeight: ${canvasRef.current.offsetHeight}px`);
        console.log(`📋 DevicePixelRatio: ${window.devicePixelRatio}`);
        
        renderer.setSize(canvasWidth, canvasHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        renderer.outputColorSpace = THREE.SRGBColorSpace;
        rendererRef.current = renderer;
        
        console.log(`📋 レンダラーサイズ: ${renderer.domElement.width}x${renderer.domElement.height}`);

        // ライティング設定
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.setScalar(PERFORMANCE_CONFIG.SHADOW_MAP_SIZE);
        scene.add(directionalLight);

        // レンダリングループ（点滅修正）
        const renderLoop = () => {
          const now = performance.now();
          const deltaTime = (now - lastFrameTimeRef.current) / 1000; // 秒単位に変換

          // 統一モデルコントローラーの更新
          if (modelControllerRef.current) {
            modelControllerRef.current.update(deltaTime);
            needsRenderRef.current = true;
          }

          // VRMアニメーション更新（モデルが読み込まれている場合）
          if (vrmRef.current?.vrm) {
            // VRMアニメーション更新
            updateAutoBlinking(deltaTime);
            updateIdleAnimation(deltaTime);

            // VRM表情マネージャーの更新
            if (vrmRef.current.vrm.expressionManager) {
              vrmRef.current.vrm.expressionManager.update();
            }

            // VRMヒューマノイドの更新
            if (vrmRef.current.vrm.humanoid) {
              vrmRef.current.vrm.humanoid.update();
            }

            // 継続的なレンダリングを要求
            needsRenderRef.current = true;
          }

          // フレームレート制御付きレンダリング
          if (deltaTime >= 1000 / PERFORMANCE_CONFIG.TARGET_FPS || needsRenderRef.current) {
            // カメラとレンダリング
            if (cameraRef.current) {
              renderer.render(scene, cameraRef.current);
            }
            lastFrameTimeRef.current = now;

            // FPS監視
            performanceRef.current.frameCount++;
            if (now - performanceRef.current.lastFpsCheck > 1000) {
              performanceRef.current.fps = performanceRef.current.frameCount;
              performanceRef.current.frameCount = 0;
              performanceRef.current.lastFpsCheck = now;
            }

            // VRMが読み込まれていない場合のみレンダリング要求をリセット
            if (!vrmRef.current?.vrm) {
              needsRenderRef.current = false;
            }
          }

          animationIdRef.current = requestAnimationFrame(renderLoop);
        };

        renderLoop();
        markNeedsRender();
        
        console.log(`🎮 Three.js初期化完了: ${agentId}`);

      } catch (error) {
        console.error(`❌ Three.js初期化エラー: ${agentId}`, error);
        setError('3Dエンジンの初期化に失敗しました');
      }
    };

    initThreeJS();

    // クリーンアップ
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      clearCurrentModel();
    };
  }, [agentId, loadVRM, markNeedsRender, clearCurrentModel, showPlaceholder]);

  // ファイルアップロード処理（glTF互換性向上）
  const handleFileUpload = useCallback((file: File) => {
    const fileName = file.name.toLowerCase();
    const isValidFile = fileName.endsWith('.vrm') || fileName.endsWith('.glb') || fileName.endsWith('.gltf');

    if (file && isValidFile) {
      console.log(`📁 ファイルアップロード開始: ${agentId} - ${file.name} (${file.size} bytes)`);
      console.log(`📋 ファイル詳細:`, {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified).toISOString()
      });

      if (onVRMUpload) {
        onVRMUpload(file);
      }
    } else {
      setError('VRM、glTF、またはglBファイルを選択してください\n\n対応形式: .vrm, .glb, .gltf');
    }
  }, [agentId, onVRMUpload]);

  return (
    <div className={`relative w-full h-full ${className}`}>
      <canvas 
        ref={canvasRef}
        className="w-full h-full rounded-lg bg-gradient-to-b from-gray-800 to-gray-900"
        onClick={() => markNeedsRender()}
      />
      
      {/* ローディング表示 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
          <div className="flex flex-col items-center space-y-2">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <span className="text-sm text-white">{agentId}のVRM読み込み中...</span>
          </div>
        </div>
      )}

      {/* エラー表示 */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-500/20 rounded-lg backdrop-blur-sm">
          <div className="bg-base-100 rounded-lg p-6 max-w-md mx-4 shadow-xl border border-error/20">
            <div className="text-center">
              <div className="text-2xl mb-2">⚠️</div>
              <div className="font-bold text-error mb-3">VRM読み込みエラー</div>
              <div className="text-sm text-base-content whitespace-pre-line mb-4 text-left">
                {error}
              </div>
              <div className="flex gap-2 justify-center">
                <button 
                  className="btn btn-sm btn-ghost"
                  onClick={() => setError(null)}
                >
                  閉じる
                </button>
                <button 
                  className="btn btn-sm btn-primary"
                  onClick={() => {
                    setError(null);
                    if (vrmUrl || vrmArrayBuffer) loadVRM();
                  }}
                >
                  再試行
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* ファイルアップロード領域 */}
      {!vrmUrl && !vrmArrayBuffer && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div 
            className="border-2 border-dashed border-primary/50 rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="text-4xl mb-2">📁</div>
            <div className="text-sm text-base-content/70">
              {agentId}用3Dモデルをアップロード
            </div>
            <div className="text-xs text-base-content/50 mt-1">
              対応形式: VRM, glTF, glB
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".vrm,.glb,.gltf"
              className="hidden"
              onChange={(e) => {
                if (e.target.files?.[0]) {
                  handleFileUpload(e.target.files[0]);
                }
              }}
            />
          </div>
        </div>
      )}

      {/* VRMコントロール */}
      {showControls && modelLoaded && (
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <button 
            className="btn btn-xs btn-ghost bg-black/20 text-white tooltip tooltip-left"
            data-tip="画面を更新"
            onClick={() => markNeedsRender()}
          >
            🔄 更新
          </button>
        </div>
      )}


      {/* パフォーマンス情報（デバッグ用） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-2 left-2 text-xs text-white/50">
          FPS: {performanceRef.current.fps} | Agent: {agentId}
        </div>
      )}
    </div>
  );
}