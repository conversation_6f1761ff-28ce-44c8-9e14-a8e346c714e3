'use client';

import { useState } from 'react';
import { Download, Star, Clock, Tag, Search, Filter, ExternalLink, Code2, Shield, Zap } from 'lucide-react';

interface Plugin {
  id: string;
  name: string;
  description: string;
  author: string;
  version: string;
  downloads: number;
  rating: number;
  category: 'productivity' | 'ai' | 'ui' | 'integration' | 'utility' | 'theme';
  tags: string[];
  installed: boolean;
  lastUpdated: string;
  icon: string;
}

export default function PluginStore() {
  const [plugins, setPlugins] = useState<Plugin[]>([
    {
      id: '1',
      name: 'Auto Commit',
      description: '作業内容を自動でコミット・プッシュする賢いGit管理プラグイン',
      author: 'メタスタジオ公式',
      version: '2.1.0',
      downloads: 15420,
      rating: 4.8,
      category: 'productivity',
      tags: ['git', '自動化', '効率化'],
      installed: true,
      lastUpdated: '2日前',
      icon: '🔄'
    },
    {
      id: '2', 
      name: 'AI Code Review',
      description: 'AIがコードレビューを行い、品質向上の提案をします',
      author: 'AI Labs',
      version: '1.5.2',
      downloads: 8932,
      rating: 4.6,
      category: 'ai',
      tags: ['AI', 'コードレビュー', '品質'],
      installed: false,
      lastUpdated: '1週間前',
      icon: '🤖'
    },
    {
      id: '3',
      name: 'Neo Dark Theme',
      description: 'Neo-meta-neumorphismスタイルのダークテーマ',
      author: 'Design Studio',
      version: '3.0.0',
      downloads: 23100,
      rating: 4.9,
      category: 'theme',
      tags: ['テーマ', 'ダーク', 'UI'],
      installed: false,
      lastUpdated: '3日前',
      icon: '🌙'
    },
    {
      id: '4',
      name: 'Slack Integration',
      description: 'プロジェクトの進捗をSlackに自動通知',
      author: 'Integration Hub',
      version: '1.2.0',
      downloads: 5621,
      rating: 4.3,
      category: 'integration',
      tags: ['Slack', '通知', '連携'],
      installed: false,
      lastUpdated: '2週間前',
      icon: '💬'
    },
    {
      id: '5',
      name: 'Voice Commands',
      description: '音声でプロジェクト操作・コマンド実行',
      author: 'Voice Tech',
      version: '1.0.5',
      downloads: 3245,
      rating: 4.1,
      category: 'utility',
      tags: ['音声', 'コマンド', 'アクセシビリティ'],
      installed: false,
      lastUpdated: '1ヶ月前',
      icon: '🎤'
    },
    {
      id: '6',
      name: 'Figma Sync',
      description: 'FigmaデザインとコードをリアルタイムSync',
      author: 'Design Tools Co',
      version: '2.3.1',
      downloads: 11200,
      rating: 4.7,
      category: 'integration',
      tags: ['Figma', 'デザイン', '同期'],
      installed: true,
      lastUpdated: '5日前',
      icon: '🎨'
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'すべて', icon: '🏠' },
    { id: 'productivity', name: '生産性', icon: '⚡' },
    { id: 'ai', name: 'AI', icon: '🤖' },
    { id: 'ui', name: 'UI/UX', icon: '🎨' },
    { id: 'integration', name: '連携', icon: '🔗' },
    { id: 'utility', name: 'ユーティリティ', icon: '🔧' },
    { id: 'theme', name: 'テーマ', icon: '🎨' }
  ];

  const filteredPlugins = plugins.filter((plugin) => {
    const matchesSearch = plugin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         plugin.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         plugin.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || plugin.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const installPlugin = async (pluginId: string) => {
    try {
      // 実際のプラグインインストール処理
      const response = await fetch('/api/plugins/install', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pluginId })
      });

      if (response.ok) {
        setPlugins(prev => prev.map(p =>
          p.id === pluginId ? { ...p, installed: true, downloads: p.downloads + 1 } : p
        ));

        // 成功通知
        alert(`✅ ${plugins.find(p => p.id === pluginId)?.name} をインストールしました！`);
      } else {
        throw new Error('インストールに失敗しました');
      }
    } catch (error) {
      alert(`❌ インストールエラー: ${error}`);
    }
  };

  const uninstallPlugin = async (pluginId: string) => {
    try {
      const response = await fetch('/api/plugins/uninstall', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pluginId })
      });

      if (response.ok) {
        setPlugins(prev => prev.map(p =>
          p.id === pluginId ? { ...p, installed: false } : p
        ));

        alert(`✅ ${plugins.find(p => p.id === pluginId)?.name} をアンインストールしました`);
      } else {
        throw new Error('アンインストールに失敗しました');
      }
    } catch (error) {
      alert(`❌ アンインストールエラー: ${error}`);
    }
  };

  return (
    <div className="h-full flex flex-col neo-glass">
      {/* ヘッダー */}
      <div className="p-4 border-b border-base-content/10 bg-base-300/80">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">プラグインストア</h1>
            <p className="text-sm text-base-content/70">機能を拡張してメタスタジオをカスタマイズ</p>
          </div>
          <div className="flex items-center gap-2">
            <button className="btn btn-outline btn-sm neo-hover">
              <Code2 size={16} />
              開発者向け
            </button>
            <button className="btn btn-primary btn-sm neo-hover">
              <ExternalLink size={16} />
              プラグイン作成
            </button>
          </div>
        </div>

        {/* 検索バー */}
        <div className="flex gap-2">
          <div className="form-control flex-1">
            <div className="input-group">
              <span className="bg-base-200 p-3">
                <Search size={16} />
              </span>
              <input
                type="text"
                placeholder="プラグインを検索..."
                className="input input-bordered flex-1"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          <select 
            className="select select-bordered"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            {categories.map(cat => (
              <option key={cat.id} value={cat.id}>
                {cat.icon} {cat.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* プラグインリスト */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {filteredPlugins.map((plugin) => (
            <div key={plugin.id} className="card bg-base-200 shadow-xl neo-depth">
              <div className="card-body p-4">
                {/* ヘッダー */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-3xl">{plugin.icon}</div>
                    <div>
                      <h3 className="font-bold">{plugin.name}</h3>
                      <p className="text-xs text-base-content/60">by {plugin.author}</p>
                    </div>
                  </div>
                  <div className="badge badge-ghost badge-sm">v{plugin.version}</div>
                </div>

                {/* 説明 */}
                <p className="text-sm text-base-content/70 mb-3 line-clamp-2">
                  {plugin.description}
                </p>

                {/* タグ */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {plugin.tags.map(tag => (
                    <span key={tag} className="badge badge-outline badge-xs">
                      <Tag size={10} className="mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>

                {/* 統計 */}
                <div className="flex items-center gap-4 text-xs text-base-content/60 mb-3">
                  <div className="flex items-center gap-1">
                    <Download size={12} />
                    <span>{plugin.downloads.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star size={12} className="text-warning" />
                    <span>{plugin.rating}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock size={12} />
                    <span>{plugin.lastUpdated}</span>
                  </div>
                </div>

                {/* アクション */}
                <div className="card-actions">
                  {plugin.installed ? (
                    <div className="flex gap-2 w-full">
                      <button className="btn btn-success btn-sm flex-1" disabled>
                        インストール済み
                      </button>
                      <button 
                        className="btn btn-outline btn-error btn-sm"
                        onClick={() => uninstallPlugin(plugin.id)}
                      >
                        削除
                      </button>
                    </div>
                  ) : (
                    <button 
                      className="btn btn-primary btn-sm w-full neo-hover"
                      onClick={() => installPlugin(plugin.id)}
                    >
                      <Download size={16} />
                      インストール
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* インストール済みプラグイン情報 */}
      <div className="border-t border-base-content/10 p-4 bg-base-200/50">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Shield size={16} className="text-success" />
            <span>インストール済み: {plugins.filter(p => p.installed).length}個</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap size={16} className="text-warning" />
            <span>アクティブ: {plugins.filter(p => p.installed).length}個</span>
          </div>
        </div>
      </div>
    </div>
  );
}