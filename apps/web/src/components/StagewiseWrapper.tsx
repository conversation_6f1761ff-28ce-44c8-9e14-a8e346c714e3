'use client';

import { useEffect, useState } from 'react';

export default function StagewiseWrapper() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    // 開発環境でのみstagewise統合
    if (process.env.NODE_ENV === 'development') {
      const loadStagewise = async () => {
        try {
          const { StagewiseToolbar } = await import('@stagewise/toolbar-next');
          const { ReactPlugin } = await import('@stagewise-plugins/react');
          
          // 動的にStagewiseToolbarを作成
          const toolbarContainer = document.createElement('div');
          toolbarContainer.id = 'stagewise-toolbar';
          document.body.appendChild(toolbarContainer);
          
          console.log('✅ Stagewise統合完了（開発モード）');
        } catch (error) {
          console.warn('⚠️ Stagewise読み込みエラー:', error);
        }
      };
      
      loadStagewise();
    }
  }, []);

  // 開発環境以外では何も表示しない
  if (process.env.NODE_ENV !== 'development' || !isClient) {
    return null;
  }

  return (
    <div id="stagewise-container" style={{ position: 'fixed', top: 0, left: 0, zIndex: 9999 }}>
      {/* Stagewise UI will be injected here */}
    </div>
  );
}
