'use client';

import { useEffect, useState } from 'react';

interface NotificationSettings {
  notificationSound: boolean;
  soundType: string;
  soundVolume: number;
  taskCompleteSound: boolean;
  projectUpdateSound: boolean;
  agentReportSound: boolean;
  systemAlertSound: boolean;
  autoApproveWhitelist: string[];
  autoApproveAll: boolean;
  showConfirmDialog: boolean;
}

export function useNotifications() {
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [settings, setSettings] = useState<NotificationSettings>({
    notificationSound: true,
    soundType: 'chime',
    soundVolume: 0.7,
    taskCompleteSound: true,
    projectUpdateSound: true,
    agentReportSound: true,
    systemAlertSound: true,
    autoApproveWhitelist: [
      'claude-code-complete',
      'task-complete',
      'file-operation-complete',
      'plugin-install-complete'
    ],
    autoApproveAll: false,
    showConfirmDialog: false,
  });

  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setIsSupported(true);
      setPermission(Notification.permission);
    }
    
    // ローカルストレージから設定を読み込み
    const savedSettings = localStorage.getItem('metastudio-notification-settings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('通知設定の読み込みエラー:', error);
      }
    }
  }, []);

  // 設定をローカルストレージに保存
  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    localStorage.setItem('metastudio-notification-settings', JSON.stringify(updated));
  };

  const requestPermission = async () => {
    if (!isSupported) return false;
    
    try {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result === 'granted';
    } catch (error) {
      console.error('通知許可リクエストエラー:', error);
      return false;
    }
  };

  // 通知音再生機能
  const playNotificationSound = (soundType: string = settings.soundType) => {
    if (!settings.notificationSound) return;
    
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // 音の種類に応じて周波数を設定
      const soundFrequencies = {
        chime: [800, 600, 400],
        bell: [440, 523, 659],
        success: [261, 329, 392, 523],
        alert: [880, 880, 880],
        subtle: [200, 300]
      };
      
      const frequencies = soundFrequencies[soundType as keyof typeof soundFrequencies] || soundFrequencies.chime;
      let currentNote = 0;
      
      const playNote = () => {
        if (currentNote >= frequencies.length) {
          oscillator.stop();
          return;
        }
        
        oscillator.frequency.setValueAtTime(frequencies[currentNote], audioContext.currentTime);
        gainNode.gain.setValueAtTime(settings.soundVolume * 0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        currentNote++;
        setTimeout(playNote, 200);
      };
      
      oscillator.start();
      playNote();
    } catch (error) {
      console.error('通知音再生エラー:', error);
    }
  };

  // 自動承認チェック機能
  const shouldAutoApprove = (tag?: string) => {
    if (settings.autoApproveAll) return true;
    if (!tag) return false;
    return settings.autoApproveWhitelist.includes(tag);
  };

  // 確認ダイアログ表示機能
  const showConfirmationDialog = (title: string, message: string, tag?: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (shouldAutoApprove(tag)) {
        console.log(`✅ 自動承認: ${title} - ${message}`);
        resolve(true);
        return;
      }

      if (!settings.showConfirmDialog) {
        resolve(true);
        return;
      }

      const confirmed = confirm(`${title}\n\n${message}\n\n続行しますか？`);
      resolve(confirmed);
    });
  };

  const sendNotification = (title: string, options?: NotificationOptions & { tag?: string }) => {
    if (!isSupported || permission !== 'granted') {
      console.log('通知未許可またはサポートされていません');
      return;
    }

    // 自動承認チェック
    if (options?.tag && shouldAutoApprove(options.tag)) {
      console.log(`🔔 自動承認通知: ${title}`);
    }

    try {
      const notification = new Notification(title, {
        icon: '/meta-studio-logo.png',
        badge: '/meta-studio-logo.png',
        requireInteraction: false, // 自動承認のため常にfalse
        ...options,
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // 自動承認の場合は短時間で自動クローズ
      if (options?.tag && shouldAutoApprove(options.tag)) {
        setTimeout(() => {
          notification.close();
        }, 3000); // 3秒後に自動クローズ
      }

      return notification;
    } catch (error) {
      console.error('通知送信エラー:', error);
    }
  };

  const sendTaskCompleteNotification = (taskName: string) => {
    // 通知音を再生（設定に応じて）
    if (settings.taskCompleteSound) {
      playNotificationSound(settings.soundType);
    }
    
    sendNotification('タスク完了 🎉', {
      body: `${taskName} が完了しました！`,
      tag: 'task-complete',
      requireInteraction: false,
    });
  };

  const sendClaudeCodeCompleteNotification = (taskDescription?: string) => {
    // 通知音を再生（Claude Code専用設定）
    if (settings.taskCompleteSound) {
      playNotificationSound(settings.soundType);
    }
    
    const body = taskDescription 
      ? `Claude Code タスク完了: ${taskDescription}`
      : 'Claude Code がタスクを完了しました！';
    
    sendNotification('Claude Code 完了 🤖', {
      body,
      tag: 'claude-code-complete',
      requireInteraction: false,
      icon: '/claude-logo.png',
    });
    
    // コンソールにも表示
    console.log('🤖 Claude Code タスク完了通知:', taskDescription || 'タスク完了');
  };

  const sendProjectNotification = (projectName: string, status: string) => {
    // 通知音を再生（設定に応じて）
    if (settings.projectUpdateSound) {
      const soundType = status === 'failed' ? 'alert' : 'success';
      playNotificationSound(soundType);
    }

    const messages = {
      deployed: `${projectName} のデプロイが完了しました！`,
      completed: `${projectName} が完成しました！`,
      failed: `${projectName} でエラーが発生しました`,
    };

    sendNotification('プロジェクト更新', {
      body: messages[status as keyof typeof messages] || `${projectName} の状態: ${status}`,
      tag: 'project-update',
    });
  };

  const sendAgentNotification = (agentName: string, message: string) => {
    // 通知音を再生（設定に応じて）
    if (settings.agentReportSound) {
      playNotificationSound('bell');
    }

    sendNotification(`エージェント: ${agentName}`, {
      body: message,
      tag: 'agent-message',
    });
  };

  const sendSystemAlertNotification = (title: string, message: string) => {
    // システムアラート通知音を再生（設定に応じて）
    if (settings.systemAlertSound) {
      playNotificationSound('alert');
    }

    sendNotification(title, {
      body: message,
      tag: 'system-alert',
      requireInteraction: true,
    });
  };

  // ホワイトリスト管理機能
  const addToWhitelist = (tag: string) => {
    setSettings(prev => ({
      ...prev,
      autoApproveWhitelist: [...prev.autoApproveWhitelist, tag]
    }));
  };

  const removeFromWhitelist = (tag: string) => {
    setSettings(prev => ({
      ...prev,
      autoApproveWhitelist: prev.autoApproveWhitelist.filter(t => t !== tag)
    }));
  };

  const clearWhitelist = () => {
    setSettings(prev => ({
      ...prev,
      autoApproveWhitelist: []
    }));
  };

  const resetToDefaultWhitelist = () => {
    setSettings(prev => ({
      ...prev,
      autoApproveWhitelist: [
        'claude-code-complete',
        'task-complete',
        'file-operation-complete',
        'plugin-install-complete'
      ]
    }));
  };

  return {
    isSupported,
    permission,
    settings,
    updateSettings,
    requestPermission,
    sendNotification,
    sendTaskCompleteNotification,
    sendClaudeCodeCompleteNotification,
    sendProjectNotification,
    sendAgentNotification,
    sendSystemAlertNotification,
    playNotificationSound,
    showConfirmationDialog,
    shouldAutoApprove,
    addToWhitelist,
    removeFromWhitelist,
    clearWhitelist,
    resetToDefaultWhitelist,
  };
}