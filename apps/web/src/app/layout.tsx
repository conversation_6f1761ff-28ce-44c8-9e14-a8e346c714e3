import type { Metada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { StagewiseToolbar } from '@stagewise/toolbar-next';
import { ReactPlugin } from '@stagewise-plugins/react';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "メタスタジオ - 脳内現実化ツール",
  description: "アイデアから実装まで、あらゆる創造的願望を自動実現するオールラウンド開発プラットフォーム",
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html 
      lang="ja" 
      data-theme="metastudio"
      suppressHydrationWarning={true}
      className="h-full bg-base-100"
    >
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full m-0 p-0 bg-base-100 text-base-content`}
        suppressHydrationWarning={true}
      >
        <StagewiseToolbar
          config={{
            plugins: [ReactPlugin]
          }}
        />
        {children}
      </body>
    </html>
  );
}
