import { NextRequest, NextResponse } from 'next/server';
import { rm } from 'fs/promises';
import { join } from 'path';

export async function POST(request: NextRequest) {
  try {
    const { pluginId } = await request.json();
    
    if (!pluginId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Plugin ID is required' 
      }, { status: 400 });
    }

    const pluginsDir = join(process.cwd(), 'public', 'plugins', pluginId);
    
    // プラグインディレクトリを削除
    await rm(pluginsDir, { recursive: true, force: true });
    
    return NextResponse.json({
      success: true,
      message: `Plugin ${pluginId} uninstalled successfully`
    });
    
  } catch (error) {
    console.error('Plugin uninstallation error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to uninstall plugin'
    }, { status: 500 });
  }
}
