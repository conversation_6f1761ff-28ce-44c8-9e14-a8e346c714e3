import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

// プラグイン定義
const AVAILABLE_PLUGINS = {
  '1': {
    id: '1',
    name: 'Auto Commit',
    files: {
      'auto-commit.js': `
// Auto Commit Plugin
export class AutoCommitPlugin {
  constructor() {
    this.name = 'Auto Commit';
    this.version = '2.1.0';
  }

  async init() {
    console.log('🔄 Auto Commit Plugin initialized');
    this.setupAutoCommit();
  }

  setupAutoCommit() {
    // 5分ごとに自動コミット
    setInterval(() => {
      this.autoCommit();
    }, 5 * 60 * 1000);
  }

  async autoCommit() {
    try {
      const response = await fetch('/api/git/auto-commit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: 'Auto commit: ' + new Date().toISOString() })
      });
      
      if (response.ok) {
        console.log('✅ Auto commit successful');
      }
    } catch (error) {
      console.error('❌ Auto commit failed:', error);
    }
  }
}
      `
    }
  },
  '2': {
    id: '2',
    name: 'AI Code Review',
    files: {
      'ai-code-review.js': `
// AI Code Review Plugin
export class AICodeReviewPlugin {
  constructor() {
    this.name = 'AI Code Review';
    this.version = '1.5.2';
  }

  async init() {
    console.log('🤖 AI Code Review Plugin initialized');
    this.setupCodeReview();
  }

  setupCodeReview() {
    // ファイル保存時にコードレビュー
    document.addEventListener('file-saved', (event) => {
      this.reviewCode(event.detail.filePath);
    });
  }

  async reviewCode(filePath) {
    try {
      const response = await fetch('/api/ai/code-review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath })
      });
      
      const review = await response.json();
      this.showReviewResults(review);
    } catch (error) {
      console.error('❌ Code review failed:', error);
    }
  }

  showReviewResults(review) {
    // レビュー結果を表示
    console.log('📝 Code Review Results:', review);
  }
}
      `
    }
  },
  '5': {
    id: '5',
    name: 'Voice Commands',
    files: {
      'voice-commands.js': `
// Voice Commands Plugin
export class VoiceCommandsPlugin {
  constructor() {
    this.name = 'Voice Commands';
    this.version = '1.0.5';
    this.recognition = null;
  }

  async init() {
    console.log('🎤 Voice Commands Plugin initialized');
    this.setupVoiceRecognition();
  }

  setupVoiceRecognition() {
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new webkitSpeechRecognition();
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = 'ja-JP';

      this.recognition.onresult = (event) => {
        const command = event.results[event.results.length - 1][0].transcript;
        this.processVoiceCommand(command);
      };

      this.recognition.start();
    }
  }

  processVoiceCommand(command) {
    console.log('🎤 Voice command:', command);
    
    if (command.includes('ファイル作成')) {
      this.createFile();
    } else if (command.includes('保存')) {
      this.saveFile();
    } else if (command.includes('実行')) {
      this.runCommand();
    }
  }

  createFile() {
    console.log('📄 Creating new file via voice command');
  }

  saveFile() {
    console.log('💾 Saving file via voice command');
  }

  runCommand() {
    console.log('⚡ Running command via voice');
  }
}
      `
    }
  }
};

export async function POST(request: NextRequest) {
  try {
    const { pluginId } = await request.json();
    
    if (!pluginId || !AVAILABLE_PLUGINS[pluginId]) {
      return NextResponse.json({ 
        success: false, 
        error: 'Plugin not found' 
      }, { status: 404 });
    }

    const plugin = AVAILABLE_PLUGINS[pluginId];
    const pluginsDir = join(process.cwd(), 'public', 'plugins', pluginId);
    
    // プラグインディレクトリを作成
    await mkdir(pluginsDir, { recursive: true });
    
    // プラグインファイルを作成
    for (const [filename, content] of Object.entries(plugin.files)) {
      const filePath = join(pluginsDir, filename);
      await writeFile(filePath, content, 'utf8');
    }
    
    // プラグイン設定ファイルを作成
    const configPath = join(pluginsDir, 'plugin.json');
    const config = {
      id: plugin.id,
      name: plugin.name,
      installed: true,
      installedAt: new Date().toISOString(),
      files: Object.keys(plugin.files)
    };
    
    await writeFile(configPath, JSON.stringify(config, null, 2), 'utf8');
    
    return NextResponse.json({
      success: true,
      message: `Plugin ${plugin.name} installed successfully`,
      plugin: config
    });
    
  } catch (error) {
    console.error('Plugin installation error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to install plugin'
    }, { status: 500 });
  }
}
